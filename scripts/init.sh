#!/bin/sh
set -e

echo "Waiting for PostgreSQL database..."
# Maximum number of retries
max_retries=30
counter=0

until nc -z postgres 5432 || [ $counter -eq $max_retries ]; do
    counter=$((counter+1))
    echo "Waiting for PostgreSQL to be ready... ($counter/$max_retries)"
    sleep 2
done

if [ $counter -eq $max_retries ]; then
    echo "Error: Failed to connect to PostgreSQL within the allocated time."
    exit 1
fi

echo "PostgreSQL is up and running!"

# Wait a bit to ensure PostgreSQL is fully ready
sleep 5

echo "Running database migrations..."
echo "Database connection info:"
echo "DB_HOST: $DB_HOST"
echo "DB_PORT: $DB_PORT"
echo "DB_USERNAME: $DB_USERNAME"
echo "DB_DATABASE: $DB_DATABASE"

# Try to connect to the database to verify connection
echo "Testing database connection..."
nc -z $DB_HOST $DB_PORT && echo "Connection successful!" || echo "Connection failed!"

# Run the patch script to fix crypto issue
echo "Running patch for crypto issue..."
node /usr/src/app/scripts/crypto-patch.js

# Run migrations
echo "Running migrations..."
node /usr/src/app/scripts/run-migrations.js || {
    echo "Migration failed but continuing..."
}

# Starting the application
echo "Starting the application..."
exec node dist/main