/* eslint-disable @typescript-eslint/no-var-requires */
// Universal solution to patch all occurrences of crypto.randomUUID in NestJS modules
const fs = require('fs');
const path = require('path');

console.log('Running comprehensive crypto patch script...');

// Define the NestJS node_modules directories to search
const nestModulesDir = path.join(__dirname, '../node_modules/@nestjs');

// Function to recursively get all .js files in a directory
function getJsFilesRecursively(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      getJsFilesRecursively(filePath, fileList);
    } else if (file.endsWith('.js')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to check if a file contains the pattern
function fileContainsCrypto(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return content.includes('crypto.randomUUID');
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return false;
  }
}

// Function to patch a single file
function patchFile(filePath) {
  console.log(`Checking file: ${filePath}`);

  try {
    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      return;
    }

    // Skip if file doesn't contain crypto.randomUUID
    if (!fileContainsCrypto(filePath)) {
      return;
    }

    console.log(`Patching file: ${filePath}`);

    // Read file content
    let fileContent = fs.readFileSync(filePath, 'utf8');

    // Replace crypto.randomUUID() calls
    const originalContent = fileContent;
    fileContent = fileContent.replace(
      /crypto\.randomUUID\(\)/g,
      `(function() { 
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          const r = Math.random() * 16 | 0;
          const v = c === 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      })()`,
    );

    // Check if any replacements were made
    if (fileContent === originalContent) {
      console.log(`No replacements made in ${filePath}`);
      return;
    }

    // Save the patched file
    fs.writeFileSync(filePath, fileContent);
    console.log(`Successfully patched: ${filePath}`);
  } catch (error) {
    console.error(`Error patching ${filePath}:`, error.message);
  }
}

// Function to patch all files in a directory
function patchAllFiles() {
  try {
    // Start with known problematic files for efficiency
    const knownFiles = [
      path.join(nestModulesDir, 'typeorm/dist/common/typeorm.utils.js'),
      path.join(nestModulesDir, 'schedule/dist/scheduler.orchestrator.js'),
    ];

    console.log('Patching known problematic files first...');
    knownFiles.forEach(patchFile);

    // Then scan all NestJS modules for any other instances
    console.log('Scanning all NestJS modules for crypto.randomUUID...');
    const allJsFiles = getJsFilesRecursively(nestModulesDir);
    console.log(`Found ${allJsFiles.length} JS files to check`);

    // Process files in smaller batches to avoid memory issues
    const batchSize = 50;
    for (let i = 0; i < allJsFiles.length; i += batchSize) {
      const batch = allJsFiles.slice(i, i + batchSize);
      batch.forEach(patchFile);
      console.log(
        `Processed ${Math.min(i + batchSize, allJsFiles.length)} of ${allJsFiles.length} files`,
      );
    }
  } catch (error) {
    console.error('Error during patching:', error.message);
  }
}

// Start patching
try {
  patchAllFiles();
  console.log('Crypto patch completed successfully.');
} catch (error) {
  console.error('Fatal error during patching:', error);
  process.exit(1);
}
