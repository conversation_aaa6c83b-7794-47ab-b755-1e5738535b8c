# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=auto_login

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=1h
JWT_REFRESH_EXPIRATION=7d

# Proxy Configuration
PROXY_HOST=proxy.example.com
PROXY_PORT=8080
PROXY_USERNAME=proxy_user
PROXY_PASSWORD=proxy_password

# Discord Configuration
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_CALLBACK_URL=http://localhost:3000/auth/discord/callback
DISCORD_GUILD_ID=your_discord_guild_id
DISCORD_VIP_PRIVATE_CATEGORY_ID=your_DISCORD_VIP_PRIVATE_CATEGORY_ID
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_INVITE_CODE=your_discord_invite_code
DISCORD_MEMBER_ROLE_ID=1346698476712493068
DISCORD_INVITE_CALLBACK_URL=
DISCORD_GENERAL_CHAT_CHANNEL_ID=
# VIP COMMUNITY
DISCORD_VIP_BUYER_ROLE_ID=
# DESIGN COMMUNITY
DISCORD_DESIGN_BUYER_ROLE_ID=
# MARKETING COMMUNITY
DISCORD_MARKETING_BUYER_ROLE_ID=

# Mail Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_SECURE=false
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_FROM=<EMAIL>
MAIL_FEEDBACK_ID=your_mail_feedback_id

# PayPal Configuration
# For development, use your PayPal Sandbox credentials
# For production, use your PayPal Live credentials
PAYPAL_CLIENT_ID=your_paypal_sandbox_client_id
PAYPAL_CLIENT_SECRET=your_paypal_sandbox_client_secret

# Application Configuration
APP_URL=http://localhost:3000
NODE_ENV=development

# Google Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback