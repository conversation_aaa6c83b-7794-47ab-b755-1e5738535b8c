import {
  Controller,
  Post,
  Body,
  Param,
  Get,
  UseGuards,
  Request,
  UnauthorizedException,
} from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller('payments')
@UseGuards(JwtAuthGuard)
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post('create-order')
  async createPaypalOrder(
    @Request() req: { user: { sub: number } },
    @Body() createPaymentDto: CreatePaymentDto,
  ) {
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not authenticated');
    }
    createPaymentDto.user_id = req.user.sub;
    return this.paymentsService.createPaypalOrder(createPaymentDto);
  }

  @Post('capture/:orderId/:paymentId')
  async capturePaypalPayment(
    @Param('orderId') orderId: string,
    @Param('paymentId') paymentId: number,
  ) {
    return this.paymentsService.capturePaypalPayment(orderId, paymentId);
  }

  @Get(':id')
  async getPayment(@Param('id') id: number) {
    return this.paymentsService.getPaymentById(id);
  }
}
