import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Payment } from './entities/payment.entity';
import { PaymentsService } from './payments.service';
import { PaymentsController } from './payments.controller';
import { UsersModule } from '../users/users.module';
import { DiscordModule } from '../discord/discord.module';
import { InviteLinksModule } from '../invite-links/invite-links.module';
import { PackagesModule } from '../packages/packages.module';
import { MailerModule } from '@nestjs-modules/mailer';

@Module({
  imports: [
    TypeOrmModule.forFeature([Payment]),
    UsersModule,
    DiscordModule,
    InviteLinksModule,
    PackagesModule,
    MailerModule,
  ],
  providers: [PaymentsService],
  controllers: [PaymentsController],
  exports: [PaymentsService],
})
export class PaymentsModule {}
