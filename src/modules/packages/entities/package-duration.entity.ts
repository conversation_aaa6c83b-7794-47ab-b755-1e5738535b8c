import {
  <PERSON><PERSON>ty,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Package } from './package.entity';

@Entity('package_durations')
export class PackageDuration {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  package_id: number;

  @Column()
  duration_days: number;

  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  discount_percent: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => Package, (package_) => package_.durations)
  @JoinColumn({ name: 'package_id' })
  package: Package;
}
