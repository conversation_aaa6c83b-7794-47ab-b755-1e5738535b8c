import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
} from 'typeorm';
import { PackageUser } from './package-user.entity';
import { PackageDuration } from './package-duration.entity';
import { Account } from '../../accounts/entities/account.entity';

export interface PackageFeature {
  description: string;
  img: string;
}

@Entity('packages')
export class Package {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('jsonb', { nullable: true })
  features: PackageFeature[];

  @Column({ default: false })
  is_best_choice: boolean;

  @Column({ default: false })
  has_trail: boolean;

  @Column({ type: 'int', default: 0 })
  sort: number;

  @OneToMany(() => PackageUser, (packageUser) => packageUser.package)
  packageUsers: PackageUser[];

  @OneToMany(
    () => PackageDuration,
    (packageDuration) => packageDuration.package,
  )
  durations: PackageDuration[];

  @ManyToMany(() => Account, (account) => account.packages)
  accounts: Account[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
