import {
  Controller,
  Get,
  Query,
  Param,
  HttpException,
  HttpStatus,
  ParseIntPipe,
  NotFoundException,
} from '@nestjs/common';
import { PackagesService } from './packages.service';

@Controller('public/packages')
export class PublicPackagesController {
  constructor(private readonly packagesService: PackagesService) {}

  @Get('durations/filter')
  async getPackageDurations(
    @Query('months') months?: string,
    @Query('currency') currency: string = 'USD',
  ) {
    try {
      // Convert months to days for filtering
      let days: number[] | undefined;
      if (months) {
        const monthValues = months
          .split(',')
          .map((m) => parseInt(m.trim(), 10));
        days = monthValues.map((month) => (month === 12 ? 365 : month * 30)); // Special case for 1 year (365 days)
      }

      return await this.packagesService.findPackageDurations(days, currency);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve package durations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('durations/:id')
  async getPackageDuration(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.packagesService.findPackageDuration(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to retrieve package duration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.packagesService.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to retrieve package',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
