import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PackageUser } from './entities/package-user.entity';
import { CreatePackageUserDto } from './dto/create-package-user.dto';
import { UpdatePackageUserDto } from './dto/update-package-user.dto';

@Injectable()
export class PackageUsersService {
  constructor(
    @InjectRepository(PackageUser)
    private packageUserRepository: Repository<PackageUser>,
  ) {}

  async create(
    createPackageUserDto: CreatePackageUserDto,
  ): Promise<PackageUser> {
    const newPackageUser =
      this.packageUserRepository.create(createPackageUserDto);
    return this.packageUserRepository.save(newPackageUser);
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    userId?: number,
    packageId?: number,
    status?: string,
  ) {
    const skip = (page - 1) * limit;
    const queryBuilder = this.packageUserRepository
      .createQueryBuilder('packageUser')
      .leftJoinAndSelect('packageUser.user', 'user')
      .leftJoinAndSelect('packageUser.package', 'package')
      .orderBy('packageUser.created_at', 'DESC')
      .skip(skip)
      .take(limit);

    if (userId) {
      queryBuilder.andWhere('packageUser.user_id = :userId', { userId });
    }

    if (packageId) {
      queryBuilder.andWhere('packageUser.package_id = :packageId', {
        packageId,
      });
    }

    if (status) {
      queryBuilder.andWhere('packageUser.status = :status', { status });
    }

    const [packageUsers, total] = await queryBuilder.getManyAndCount();

    return {
      data: packageUsers,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<PackageUser> {
    const packageUser = await this.packageUserRepository.findOne({
      where: { id },
      relations: ['user', 'package'],
    });

    if (!packageUser) {
      throw new NotFoundException(`PackageUser with ID ${id} not found`);
    }

    return packageUser;
  }

  async findByUserAndPackage(
    userId: number,
    packageId: number,
  ): Promise<PackageUser | null> {
    return this.packageUserRepository.findOne({
      where: { user_id: userId, package_id: packageId },
      relations: ['user', 'package'],
    });
  }

  async findActiveByUser(userId: number): Promise<PackageUser[]> {
    return this.packageUserRepository.find({
      where: { user_id: userId, status: 'active' },
      relations: ['package'],
    });
  }

  async update(
    id: number,
    updatePackageUserDto: UpdatePackageUserDto,
  ): Promise<PackageUser> {
    const packageUser = await this.findOne(id);
    this.packageUserRepository.merge(packageUser, updatePackageUserDto);
    return this.packageUserRepository.save(packageUser);
  }

  async remove(id: number): Promise<void> {
    const packageUser = await this.findOne(id);
    await this.packageUserRepository.remove(packageUser);
  }

  async updateStatus(): Promise<void> {
    // Update status of expired packages
    await this.packageUserRepository
      .createQueryBuilder()
      .update(PackageUser)
      .set({ status: 'expired' })
      .where('expires_at < :now', { now: new Date() })
      .andWhere('status = :status', { status: 'active' })
      .execute();
  }
}
