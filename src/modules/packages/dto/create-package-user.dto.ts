import {
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsDate,
  IsString,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreatePackageUserDto {
  @IsNotEmpty()
  @IsNumber()
  user_id: number;

  @IsNotEmpty()
  @IsNumber()
  package_id: number;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  start_date?: Date;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  expires_at: Date;

  @IsOptional()
  @IsString()
  status?: string;
}
