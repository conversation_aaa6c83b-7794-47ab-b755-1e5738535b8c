import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsArray,
  ValidateNested,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PackageFeatureDto } from './package-feature.dto';

class PackageDurationDto {
  @IsNotEmpty()
  @IsNumber()
  duration_days: number;

  @IsNotEmpty()
  @IsNumber()
  price: number;

  @IsOptional()
  @IsNumber()
  discount_percent?: number;
}

export class CreatePackageDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageFeatureDto)
  features?: PackageFeatureDto[];

  @IsOptional()
  @IsBoolean()
  is_best_choice?: boolean;

  @IsOptional()
  @IsBoolean()
  has_trail?: boolean;

  @IsOptional()
  @IsNumber()
  sort?: number;

  // price and duration_days fields have been removed from Package

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageDurationDto)
  durations?: PackageDurationDto[];
}
