import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  NotFoundException,
  Req,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Roles } from '../auth/roles.decorator';
import { PackagesService } from './packages.service';
import { PackageUsersService } from './package-users.service';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { RolesGuard } from '../auth/roles.guard';
import { Request } from 'express';

interface RequestWithUser extends Request {
  user: {
    sub: number;
    email: string;
    role: string;
  };
}

@Controller('packages')
@UseGuards(JwtAuthGuard)
export class PackagesController {
  constructor(
    private readonly packagesService: PackagesService,
    private readonly packageUsersService: PackageUsersService,
  ) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles('admin')
  async create(@Body() createPackageDto: CreatePackageDto) {
    try {
      return await this.packagesService.create(createPackageDto);
    } catch (error) {
      throw new HttpException(
        'Failed to create package',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
    @Query('search') search?: string,
    @Query('filter') filter?: string,
  ) {
    try {
      return await this.packagesService.findAll(page, limit, search, filter);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve packages',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.packagesService.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to retrieve package',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePackageDto: UpdatePackageDto,
  ) {
    try {
      return await this.packagesService.update(id, updatePackageDto);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to update package',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async remove(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.packagesService.remove(id);
      return { message: 'Package deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to delete package',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':packageId/users/:userId')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async assignToUser(
    @Param('packageId', ParseIntPipe) packageId: number,
    @Param('userId', ParseIntPipe) userId: number,
    @Body('expiresAt') expiresAt: Date,
    @Body('durationId') durationId?: number,
  ) {
    try {
      await this.packagesService.assignToUser(
        packageId,
        userId,
        expiresAt,
        durationId,
      );
      return { message: 'Package assigned to user successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to assign package to user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':packageId/users/:userId')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async removeFromUser(
    @Param('packageId', ParseIntPipe) packageId: number,
    @Param('userId', ParseIntPipe) userId: number,
  ) {
    try {
      await this.packagesService.removeFromUser(packageId, userId);
      return { message: 'Package removed from user successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to remove package from user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('users/my')
  async getMyPackages(@Req() req: RequestWithUser) {
    try {
      const userId = req.user.sub;
      return await this.packageUsersService.findAll(
        1,
        100,
        userId,
        undefined,
        'active',
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve your packages',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('users/:userId')
  async getUserPackages(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('status') status?: string,
  ) {
    try {
      const packageUsers = await this.packageUsersService.findAll(
        1,
        100,
        userId,
        undefined,
        status,
      );
      return packageUsers;
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve user packages',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':packageId/accounts/:accountId')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async assignToAccount(
    @Param('packageId', ParseIntPipe) packageId: number,
    @Param('accountId', ParseIntPipe) accountId: number,
  ) {
    try {
      await this.packagesService.assignToAccount(packageId, accountId);
      return { message: 'Package assigned to account successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to assign package to account',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':packageId/accounts/:accountId')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async removeFromAccount(
    @Param('packageId', ParseIntPipe) packageId: number,
    @Param('accountId', ParseIntPipe) accountId: number,
  ) {
    try {
      await this.packagesService.removeFromAccount(packageId, accountId);
      return { message: 'Package removed from account successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to remove package from account',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('durations/filter')
  async getPackageDurations(
    @Query('months') months?: string,
    @Query('currency') currency: string = 'USD',
  ) {
    try {
      // Convert months to days for filtering
      let days: number[] | undefined;
      if (months) {
        const monthValues = months
          .split(',')
          .map((m) => parseInt(m.trim(), 10));
        days = monthValues.map((month) => month * 30); // Approximate days in a month
      }

      return await this.packagesService.findPackageDurations(days, currency);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve package durations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
