import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('invite_links')
export class InviteLink {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  invite_code: string;

  @Column()
  discord_user_id: string;

  @Column()
  role_id: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'timestamp' })
  expires_at: Date;

  @Column({ default: false })
  used: boolean;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'discord_user_id', referencedColumnName: 'discord_id' })
  user: User;
}
