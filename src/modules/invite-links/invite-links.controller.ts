import {
  <PERSON>,
  Get,
  Param,
  HttpException,
  HttpStatus,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Req,
} from '@nestjs/common';
import { Response, Request } from 'express';
import { InviteLinksService } from './invite-links.service';
import { DiscordService } from '../discord/discord.service';
import 'express-session';

// Extend the Express Session interface
declare module 'express-session' {
  interface SessionData {
    pendingInviteCode?: string | null;
  }
}

// Use the standard Request type
type CustomRequest = Request;

@Controller('discord')
export class InviteLinksController {
  private readonly logger = new Logger(InviteLinksController.name);

  constructor(
    private readonly inviteLinksService: InviteLinksService,
    private readonly discordService: DiscordService,
  ) {}

  @Get('invite/:inviteCode')
  async handleInvite(
    @Param('inviteCode') inviteCode: string,
    @Res() res: Response,
    @Req() req: CustomRequest,
  ) {
    try {
      const { valid, inviteLink, error } =
        await this.inviteLinksService.isValidInvite(inviteCode);

      if (!valid) {
        this.logger.warn(`Invalid invite attempt: ${error}`);
        return res.render('discord/invite-error', { error });
      }

      try {
        await this.discordService.addUserToGuildWithRole(
          inviteLink.discord_user_id,
          inviteLink.role_id,
          inviteCode, // Pass the invite code
        );

        await this.inviteLinksService.markAsUsed(inviteLink.id);
        return res.render('discord/role-success');
      } catch (error) {
        if (
          error instanceof HttpException &&
          error.getStatus() === HttpStatus.TEMPORARY_REDIRECT
        ) {
          const { inviteUrl, redirectUrl } = error.getResponse() as {
            inviteUrl: string;
            redirectUrl: string;
          };

          // Store the invite code in session to resume after joining
          if (req.session) {
            req.session.pendingInviteCode = inviteCode;
          }

          this.logger.log(`Redirecting user to Discord invite: ${inviteUrl}`);
          this.logger.log(
            `After joining, user should be redirected to: ${redirectUrl}`,
          );

          // Render the redirect template
          return res.render('discord/invite-redirect', {
            inviteUrl,
            redirectUrl,
          });
        }

        this.logger.error(`Discord error: ${error.message}`, error.stack);
        return res.render('discord/invite-error', {
          error:
            'Failed to add you to the Discord server. Please contact support.',
        });
      }
    } catch (error) {
      this.logger.error(`Error handling invite: ${error.message}`, error.stack);
      throw new HttpException(
        'Failed to process Discord invite',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('invite/:inviteCode/complete')
  async completeInvite(
    @Param('inviteCode') inviteCode: string,
    @Res() res: Response,
    @Req() req: CustomRequest,
  ) {
    try {
      // Verify this is the same invite code stored in session
      if (!req.session || req.session.pendingInviteCode !== inviteCode) {
        this.logger.warn(
          `Invalid invite completion attempt for code: ${inviteCode}`,
        );
        return res.render('discord/invite-error', {
          error: 'Invalid invite completion attempt',
        });
      }

      // Clear the pending invite
      if (req.session) {
        req.session.pendingInviteCode = null;
      }

      // Get the invite details
      const { valid, inviteLink, error } =
        await this.inviteLinksService.isValidInvite(inviteCode);

      if (!valid) {
        this.logger.warn(`Invalid invite attempt during completion: ${error}`);
        return res.render('discord/invite-error', { error });
      }

      // Now that the user has joined the server, add the role
      this.logger.log(
        `User joined server, adding role for invite code: ${inviteCode}`,
      );

      try {
        // Try to directly check and assign the role
        const success = await this.discordService.checkAndAssignRole(
          inviteLink.discord_user_id,
          inviteLink.role_id,
        );

        if (success) {
          this.logger.log(
            `Successfully assigned role to user with invite code: ${inviteCode}`,
          );
          await this.inviteLinksService.markAsUsed(inviteLink.id);
          return res.render('discord/role-success');
        } else {
          // If direct assignment failed, try the regular method which might redirect again
          this.logger.log(
            `Direct role assignment failed, trying regular method for invite code: ${inviteCode}`,
          );

          try {
            await this.discordService.addUserToGuildWithRole(
              inviteLink.discord_user_id,
              inviteLink.role_id,
              inviteCode, // Pass the invite code
            );

            await this.inviteLinksService.markAsUsed(inviteLink.id);
            return res.render('discord/role-success');
          } catch (innerError) {
            if (
              innerError instanceof HttpException &&
              innerError.getStatus() === HttpStatus.TEMPORARY_REDIRECT
            ) {
              // User still needs to join, redirect again
              const { inviteUrl, redirectUrl } = innerError.getResponse() as {
                inviteUrl: string;
                redirectUrl: string;
              };
              this.logger.log(
                `User still needs to join server, redirecting to: ${inviteUrl}`,
              );

              // Render the redirect template
              return res.render('discord/invite-redirect', {
                inviteUrl,
                redirectUrl,
              });
            }

            throw innerError; // Re-throw for the outer catch block
          }
        }
      } catch (error) {
        this.logger.error(
          `Discord error during completion: ${error.message}`,
          error.stack,
        );
        return res.render('discord/invite-error', {
          error:
            'Failed to add you to the Discord server. Please contact support.',
        });
      }
    } catch (error) {
      this.logger.error(
        `Error completing invite: ${error.message}`,
        error.stack,
      );
      return res.render('discord/invite-error', {
        error: 'Failed to process Discord invite completion',
      });
    }
  }

  @Get('invite/:inviteCode/assign-role')
  async assignRoleDirectly(
    @Param('inviteCode') inviteCode: string,
    @Res() res: Response,
  ) {
    try {
      this.logger.log(
        `Direct role assignment requested for invite code: ${inviteCode}`,
      );

      // Get the invite details
      const { valid, inviteLink, error } =
        await this.inviteLinksService.isValidInvite(inviteCode);

      if (!valid) {
        this.logger.warn(
          `Invalid invite attempt for direct role assignment: ${error}`,
        );
        return res.render('discord/invite-error', { error });
      }

      // Try to directly assign the role
      const success = await this.discordService.checkAndAssignRole(
        inviteLink.discord_user_id,
        inviteLink.role_id,
      );

      if (success) {
        this.logger.log(
          `Successfully assigned role directly for invite code: ${inviteCode}`,
        );
        await this.inviteLinksService.markAsUsed(inviteLink.id);
        return res.render('discord/role-success');
      } else {
        // If direct assignment failed, redirect to the normal invite flow
        this.logger.log(
          `Direct role assignment failed, redirecting to normal invite flow: ${inviteCode}`,
        );
        return res.redirect(`/discord/invite/${inviteCode}`);
      }
    } catch (error) {
      this.logger.error(
        `Error in direct role assignment: ${error.message}`,
        error.stack,
      );
      return res.render('discord/invite-error', {
        error:
          'Failed to assign Discord role. Please try the regular invite process.',
      });
    }
  }
}
