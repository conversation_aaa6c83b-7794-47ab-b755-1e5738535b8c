import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from '../users/entities/user.entity';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ConfigService } from '@nestjs/config';

interface DiscordUser {
  discordId: string;
  email: string;
}

interface GoogleUser {
  googleId: string;
  email: string;
}

interface TokenPayload {
  sub: number;
  email: string;
  role: string;
  status: string;
}

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async register(registerDto: RegisterDto) {
    const existingUser = await this.usersRepository.findOne({
      where: { email: registerDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    const hashedPassword = await bcrypt.hash(registerDto.password, 10);
    const user = this.usersRepository.create({
      email: registerDto.email,
      password: hashedPassword,
      status: 'inactive',
      role: 'user',
    });

    await this.usersRepository.save(user);
    return { message: 'User registered successfully' };
  }

  // Generate both access and refresh tokens
  private generateTokens(payload: TokenPayload) {
    const accessToken = this.jwtService.sign(payload);

    // Generate refresh token with longer expiration
    const refreshToken = this.jwtService.sign(
      { ...payload, tokenType: 'refresh' },
      {
        expiresIn: this.configService.get('jwt.refreshExpiresIn') || '7d',
      },
    );

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
    };
  }

  async login(loginDto: LoginDto) {
    const user = await this.usersRepository.findOne({
      where: { email: loginDto.email },
      select: ['id', 'email', 'password', 'role', 'status'],
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(
      loginDto.password,
      user.password,
    );
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
    };
    return this.generateTokens(payload);
  }

  async refreshToken(refreshToken: string) {
    try {
      // Verify the refresh token
      const decoded = await this.jwtService.verify(refreshToken);

      // Check if it's a refresh token
      if (!decoded.tokenType || decoded.tokenType !== 'refresh') {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Check if the user still exists and is active
      const user = await this.usersRepository.findOne({
        where: { id: decoded.sub },
        select: ['id', 'email', 'role', 'status'],
      });

      if (!user || user.status !== 'active') {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Generate new tokens
      const payload = {
        sub: user.id,
        email: user.email,
        role: user.role,
        status: user.status,
      };
      return this.generateTokens(payload);
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired refresh token');
    }
  }

  async findOrCreateDiscordUser(discordUser: DiscordUser) {
    let user = await this.usersRepository.findOne({
      where: [
        { discord_id: discordUser.discordId },
        { email: discordUser.email },
      ],
      select: ['id', 'email', 'password', 'role', 'status', 'discord_id'],
    });

    if (!user) {
      // Create new user with Discord credentials
      user = this.usersRepository.create({
        email: discordUser.email,
        discord_id: discordUser.discordId,
        password: await bcrypt.hash(Math.random().toString(36), 10), // Generate random password
        status: 'inactive',
        role: 'user',
      });
      await this.usersRepository.save(user);
    } else if (!user.discord_id) {
      // Link Discord to existing email user
      user.discord_id = discordUser.discordId;
      await this.usersRepository.save(user);
    }

    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
    };
    return this.generateTokens(payload);
  }

  async validateDiscordLogin(discordUser: any) {
    return await this.findOrCreateDiscordUser({
      discordId: discordUser.id,
      email: discordUser.email,
    });
  }

  async findOrCreateGoogleUser(googleUser: GoogleUser) {
    let user = await this.usersRepository.findOne({
      where: [{ google_id: googleUser.googleId }, { email: googleUser.email }],
      select: ['id', 'email', 'password', 'role', 'status', 'google_id'],
    });

    if (!user) {
      // Create new user with Google credentials
      user = this.usersRepository.create({
        email: googleUser.email,
        google_id: googleUser.googleId,
        password: await bcrypt.hash(Math.random().toString(36), 10), // Generate random password
        status: 'inactive',
        role: 'user',
      });
      await this.usersRepository.save(user);
    } else if (!user.google_id) {
      // Link Google to existing email user
      user.google_id = googleUser.googleId;
      await this.usersRepository.save(user);
    }

    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
    };
    return this.generateTokens(payload);
  }

  async validateGoogleLogin(googleUser: any) {
    return await this.findOrCreateGoogleUser({
      googleId: googleUser.id,
      email: googleUser.email,
    });
  }

  async verifyToken(token: string) {
    try {
      const decoded = await this.jwtService.verify(token);
      return {
        id: decoded.sub,
        email: decoded.email,
        role: decoded.role,
        status: decoded.status,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  async changePassword(userId: number, changePasswordDto: ChangePasswordDto) {
    // Find the user
    const user = await this.usersRepository.findOne({
      where: { id: userId },
      select: ['id', 'email', 'password', 'role', 'status'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Check if user is admin
    if (user.role !== 'admin') {
      throw new ForbiddenException(
        'Only admin users can change their password',
      );
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(
      changePasswordDto.currentPassword,
      user.password,
    );

    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    // Check if new password and confirm password match
    if (changePasswordDto.newPassword !== changePasswordDto.confirmPassword) {
      throw new BadRequestException(
        'New password and confirm password do not match',
      );
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(changePasswordDto.newPassword, 10);

    // Update the user's password
    user.password = hashedPassword;
    await this.usersRepository.save(user);

    return { message: 'Password changed successfully' };
  }
}
