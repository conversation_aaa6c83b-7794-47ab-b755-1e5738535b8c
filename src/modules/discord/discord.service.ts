import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import {
  Client,
  GuildMember,
  GatewayIntentBits,
  InviteCreateOptions,
  User as DiscordUser,
} from 'discord.js';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DiscordService {
  private client: Client;
  private guildId: string;
  private readonly logger = new Logger(DiscordService.name);
  private pendingInvites: Map<
    string,
    { discordUserId: string; roleId: string; inviteCode: string }
  > = new Map();

  constructor(private configService: ConfigService) {
    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.GuildInvites,
        GatewayIntentBits.DirectMessages,
      ],
    });
    this.guildId = this.configService.get<string>('discord.guildId');

    // Initialize Discord client
    this.initializeDiscordClient();
  }

  private async initializeDiscordClient() {
    try {
      await this.client.login(
        this.configService.get<string>('discord.botToken'),
      );
      this.logger.log('Discord bot logged in successfully');

      // Set up event listeners
      this.setupEventListeners();
    } catch (error) {
      this.logger.error(`Failed to login Discord bot: ${error.message}`);
    }
  }

  private setupEventListeners() {
    // Listen for when a member joins the server
    this.client.on('guildMemberAdd', async (member) => {
      this.logger.log(`New member joined: ${member.user.tag} (${member.id})`);

      let roleAssigned = false;

      // Check if this user has a pending invite
      for (const [inviteId, inviteData] of this.pendingInvites.entries()) {
        if (inviteData.discordUserId === member.id) {
          this.logger.log(
            `Found pending invite for user ${member.user.tag} (${member.id}). Automatically assigning role ${inviteData.roleId}`,
          );

          try {
            // Attempt to assign the role automatically
            const success = await this.checkAndAssignRole(
              member.id,
              inviteData.roleId,
            );

            if (success) {
              this.logger.log(
                `Successfully auto-assigned role ${inviteData.roleId} to user ${member.user.tag}`,
              );

              // Remove from pending invites
              this.pendingInvites.delete(inviteId);
              roleAssigned = true;

              // We could mark the invite as used here, but we'll let the user complete the process
              // via the redirect URL to ensure proper tracking in the database
            } else {
              this.logger.warn(
                `Failed to auto-assign role to user ${member.user.tag}. User will need to complete the process manually.`,
              );
            }
          } catch (error) {
            this.logger.error(
              `Error auto-assigning role to user ${member.user.tag}: ${error.message}`,
              error.stack,
            );
          }

          break; // Found the user, no need to check other invites
        }
      }

      // If no role was assigned from pending invites and the member has no roles,
      // assign the default memberRoleId
      if (!roleAssigned && member.roles.cache.size <= 1) {
        // Size 1 means only @everyone role
        const memberRoleId = this.configService.get<string>(
          'discord.memberRoleId',
        );

        if (memberRoleId) {
          try {
            this.logger.log(
              `Assigning default member role ${memberRoleId} to new user ${member.user.tag} (${member.id})`,
            );
            await member.roles.add(memberRoleId);
            this.logger.log(
              `Successfully assigned default member role ${memberRoleId} to user ${member.user.tag}`,
            );
          } catch (error) {
            this.logger.error(
              `Error assigning default member role to user ${member.user.tag}: ${error.message}`,
              error.stack,
            );
          }
        } else {
          this.logger.warn(
            'No default memberRoleId configured in environment variables. Cannot assign default role.',
          );
        }
      }
    });

    // Log when the bot is ready
    this.client.on('ready', () => {
      this.logger.log(
        `Discord bot is ready! Logged in as ${this.client.user.tag}`,
      );
    });

    // Log errors
    this.client.on('error', (error) => {
      this.logger.error(`Discord client error: ${error.message}`, error.stack);
    });
  }

  /**
   * Check if a user is in the guild and assign role if needed
   * This can be called directly from the callback endpoint
   */
  async checkAndAssignRole(discordId: string, roleId: string) {
    try {
      this.logger.log(
        `Checking if user ${discordId} is in guild and assigning role ${roleId}`,
      );

      // Log bot permissions and status
      this.logger.log(
        `Bot is logged in: ${this.client.isReady() ? 'Yes' : 'No'}`,
      );
      this.logger.log(`Guild ID being used: ${this.guildId}`);
      this.logger.log(`Role ID being assigned: ${roleId}`);

      const guild = await this.client.guilds.fetch(this.guildId);
      this.logger.log(`Successfully fetched guild: ${guild.name}`);

      // Check bot's permissions in the guild
      const botMember = await guild.members.fetch(this.client.user.id);
      this.logger.log(
        `Bot's highest role position: ${botMember.roles.highest.position}`,
      );

      // Get the role we're trying to assign
      try {
        const roleToAssign = await guild.roles.fetch(roleId);
        this.logger.log(`Role to assign position: ${roleToAssign.position}`);

        if (botMember.roles.highest.position <= roleToAssign.position) {
          this.logger.error(
            `Bot's role (${botMember.roles.highest.name}) is not high enough to assign role ${roleToAssign.name}`,
          );
        }
      } catch (roleError) {
        this.logger.error(
          `Failed to fetch role ${roleId}: ${roleError.message}`,
        );
      }

      try {
        const member = await guild.members.fetch(discordId);
        this.logger.log(
          `Found member in guild during callback: ${member.user.tag}`,
        );

        // Remove old member role if exists
        const memberRoleId = this.configService.get<string>(
          'discord.memberRoleId',
        );
        if (memberRoleId) {
          try {
            await member.roles.remove(memberRoleId);
            this.logger.log(`Removed member role from user ${member.user.tag}`);
          } catch (error) {
            this.logger.warn(`Failed to remove member role: ${error.message}`);
          }
        }

        // Add new buyer role
        try {
          this.logger.log(
            `Attempting to add role ${roleId} to user ${member.user.tag}...`,
          );
          await member.roles.add(roleId);
          this.logger.log(
            `Successfully added role ${roleId} to user ${member.user.tag} during callback`,
          );
          return true;
        } catch (roleError) {
          this.logger.error(
            `Failed to add role ${roleId} to user ${member.user.tag}: ${roleError.message}`,
          );
          this.logger.error(`Error details: ${JSON.stringify(roleError)}`);
          throw new Error(`Failed to add role: ${roleError.message}`);
        }
      } catch (error) {
        this.logger.warn(
          `User ${discordId} still not in server during callback check: ${error.message}`,
        );
        return false;
      }
    } catch (error) {
      this.logger.error(
        `Error checking and assigning role: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  async addUserToGuildWithRole(
    discordId: string,
    roleId: string,
    inviteCode?: string,
  ) {
    try {
      this.logger.log(`Attempting to add role ${roleId} to user ${discordId}`);
      const guild = await this.client.guilds.fetch(this.guildId);

      let member: GuildMember;
      try {
        member = await guild.members.fetch(discordId);
        this.logger.log(`Found member in guild: ${member.user.tag}`);
      } catch (error) {
        this.logger.warn(
          `User ${discordId} not in server, creating invite with code: ${inviteCode || 'none'}`,
        );
        // User not in server - create temporary invite and return it
        const invite = await this.createTemporaryInvite(
          inviteCode,
          discordId,
          roleId,
        );

        // Get the callback URL for this invite
        const baseUrl = this.configService.get<string>('API_URL');
        const callbackPath = inviteCode
          ? `/discord/invite/${inviteCode}/complete`
          : '';
        const redirectUrl = baseUrl + callbackPath;

        this.logger.log(`Redirect URL after Discord join: ${redirectUrl}`);

        throw new HttpException(
          {
            message: 'User needs to join server first',
            inviteUrl: invite.url,
            redirectUrl: redirectUrl,
          },
          HttpStatus.TEMPORARY_REDIRECT,
        );
      }

      if (member) {
        // Remove old member role if exists
        const memberRoleId = this.configService.get<string>(
          'discord.memberRoleId',
        );
        if (memberRoleId) {
          try {
            await member.roles.remove(memberRoleId);
            this.logger.log(`Removed member role from user ${member.user.tag}`);
          } catch (error) {
            this.logger.warn(`Failed to remove member role: ${error.message}`);
          }
        }

        // Add new buyer role
        try {
          await member.roles.add(roleId);
          this.logger.log(
            `Successfully added role ${roleId} to user ${member.user.tag}`,
          );
        } catch (roleError) {
          this.logger.error(
            `Failed to add role ${roleId} to user ${member.user.tag}: ${roleError.message}`,
          );
          throw new Error(`Failed to add role: ${roleError.message}`);
        }

        return true;
      }
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(
        `Error adding user to Discord guild with role: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to add user to Discord guild with role',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async createTemporaryInvite(
    inviteCode?: string,
    discordUserId?: string,
    roleId?: string,
  ) {
    try {
      const guild = await this.client.guilds.fetch(this.guildId);
      const channels = await guild.channels.fetch();

      // Find the first text channel to create invite
      const channel = channels.find(
        (c) => c.type === 0, // GUILD_TEXT
      );

      if (!channel) {
        throw new Error('No suitable channel found for creating invite');
      }

      // Get the callback URL from config and format it with the invite code
      let callbackUrl = this.configService.get<string>(
        'discord.inviteCallbackUrl',
      );
      if (inviteCode) {
        callbackUrl = callbackUrl.replace('{inviteCode}', inviteCode);
      }
      this.logger.log(
        `Discord invite callback URL configured as: ${callbackUrl}`,
      );

      const inviteOptions: InviteCreateOptions = {
        maxAge: 1800, // 30 minutes
        maxUses: 1, // Single use
        unique: true,
      };

      const invite = await channel.createInvite(inviteOptions);
      this.logger.log(`Created temporary invite: ${invite.url}`);

      // Store the invite information for automatic role assignment
      if (inviteCode && discordUserId && roleId) {
        const inviteId = invite.code;
        this.pendingInvites.set(inviteId, {
          discordUserId,
          roleId,
          inviteCode,
        });
        this.logger.log(
          `Stored pending invite ${inviteId} for user ${discordUserId} with role ${roleId}`,
        );
      }

      return invite;
    } catch (error) {
      this.logger.error(`Failed to create temporary invite: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send a direct message to a Discord user
   * @param discordId The Discord user ID to send the message to
   * @param message The message content to send
   * @returns True if the message was sent successfully, false otherwise
   */
  async sendDirectMessage(
    discordId: string,
    message: string,
  ): Promise<boolean> {
    try {
      this.logger.log(`Attempting to send DM to Discord user ${discordId}`);

      // Make sure the client is ready
      if (!this.client.isReady()) {
        this.logger.error('Discord client is not ready');
        return false;
      }

      // Fetch the user
      let user: DiscordUser;
      try {
        user = await this.client.users.fetch(discordId);
        this.logger.log(`Found Discord user: ${user.tag}`);
      } catch (error) {
        this.logger.error(
          `Failed to fetch Discord user ${discordId}: ${error.message}`,
        );
        return false;
      }

      // Send the DM
      try {
        const dmChannel = await user.createDM();
        await dmChannel.send(message);
        this.logger.log(`Successfully sent DM to user ${user.tag}`);
        return true;
      } catch (error) {
        this.logger.error(
          `Failed to send DM to user ${user.tag}: ${error.message}`,
        );
        return false;
      }
    } catch (error) {
      this.logger.error(
        `Error sending DM to Discord user ${discordId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Remove a role from a user and add another role
   * This is used when a user's subscription expires and we need to change their role
   * from buyer to member
   */
  async changeUserRole(
    discordId: string,
    removeRoleId: string,
    addRoleId: string,
  ): Promise<boolean> {
    try {
      this.logger.log(
        `Changing role for Discord user ${discordId} from ${removeRoleId} to ${addRoleId}`,
      );

      const guild = await this.client.guilds.fetch(this.guildId);

      try {
        const member = await guild.members.fetch(discordId);

        // First add the new role to ensure the user doesn't lose access
        try {
          await member.roles.add(addRoleId);
          this.logger.log(
            `Successfully added role ${addRoleId} to user ${member.user.tag}`,
          );
        } catch (addError) {
          this.logger.error(
            `Failed to add role ${addRoleId} to user ${member.user.tag}: ${addError.message}`,
          );
          return false;
        }

        // Then remove the old role
        try {
          await member.roles.remove(removeRoleId);
          this.logger.log(
            `Successfully removed role ${removeRoleId} from user ${member.user.tag}`,
          );
          return true;
        } catch (removeError) {
          this.logger.error(
            `Failed to remove role ${removeRoleId} from user ${member.user.tag}: ${removeError.message}`,
          );
          // We still return true because the user has the new role
          return true;
        }
      } catch (memberError) {
        this.logger.error(
          `Failed to fetch Discord member ${discordId}: ${memberError.message}`,
        );
        return false;
      }
    } catch (error) {
      this.logger.error(
        `Error changing role for Discord user ${discordId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
