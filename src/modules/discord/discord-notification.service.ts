import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// import { SchedulerRegistry } from '@nestjs/schedule';
import {
  Client,
  TextChannel,
  EmbedBuilder,
  ButtonBuilder,
  ActionRowBuilder,
  ButtonStyle,
  GatewayIntentBits,
} from 'discord.js';
import { fakerTH } from '@faker-js/faker';

@Injectable()
export class DiscordNotificationService {
  private client: Client;
  private readonly logger = new Logger(DiscordNotificationService.name);
  private generalChatChannelId: string;
  // private guildId: string;
  private paymentUrl: string;

  constructor(
    private configService: ConfigService,
    // private schedulerRegistry: SchedulerRegistry,
  ) {
    this.client = new Client({
      intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages],
    });

    this.generalChatChannelId = this.configService.get<string>(
      'discord.generalChatChannelId',
    );
    // this.guildId = this.configService.get<string>('discord.guildId');
    this.paymentUrl = this.configService.get<string>('APP_URL') + '/payment';

    this.initializeDiscordClient();
  }

  private async initializeDiscordClient() {
    try {
      await this.client.login(
        this.configService.get<string>('discord.botToken'),
      );
      this.logger.log('Discord notification bot logged in successfully');
    } catch (error) {
      this.logger.error(
        `Failed to login Discord notification bot: ${error.message}`,
      );
    }
  }

  /**
   * Generate a random Thai username using faker
   */
  private generateRandomUsername(): string {
    const fullName = fakerTH.person.fullName();
    const [firstName] = fullName.split(' ');
    return `${firstName.toLowerCase()}`;
  }

  /**
   * Send a payment success notification to the general chat channel
   */
  async sendPaymentSuccessNotification() {
    try {
      if (!this.client.isReady()) {
        this.logger.error('Discord client is not ready');
        return false;
      }

      const channel = (await this.client.channels.fetch(
        this.generalChatChannelId,
      )) as TextChannel;
      if (!channel) {
        this.logger.error(
          `Channel with ID ${this.generalChatChannelId} not found`,
        );
        return false;
      }

      const username = this.generateRandomUsername();

      const embed = new EmbedBuilder()
        .setColor('#5865F2')
        .setTitle(`**${username} has just subscribed!**`)
        .setDescription(
          "Stop waiting for miracles. Take action! ✅\nFind and copy winning products! Don't test blindly! Test with data and statistics.",
        )
        .setImage('https://stealthpack.com/assets/images/banner.jpg');

      const button = new ButtonBuilder()
        .setURL(this.paymentUrl)
        .setLabel('CLICK HERE TO UPGRADE ✅')
        .setStyle(ButtonStyle.Link);

      const row = new ActionRowBuilder<ButtonBuilder>().addComponents(button);

      await channel.send({ embeds: [embed], components: [row] });

      this.logger.log(
        `Successfully sent payment success notification to general chat channel`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Error sending payment success notification: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Schedule a random time each day at 00:00
   */
  // @Cron('0 0 0 * * *')
  // scheduleRandomNotification() {
  //   // Generate random hour (0-23) and minute (0-59)
  //   const randomHour = Math.floor(Math.random() * 24);
  //   const randomMinute = Math.floor(Math.random() * 60);
  //   const randomTime = new Date();
  //   randomTime.setHours(randomHour, randomMinute, 0, 0);

  //   // Create a new CronJob for the random time
  //   const job = new CronJob(randomTime, () => {
  //     this.sendPaymentSuccessNotification();
  //   });

  //   // Add the cron job to the scheduler registry and start it
  //   this.schedulerRegistry.addCronJob('randomNotification', job);
  //   job.start();

  //   this.logger.log(`Scheduled notification at ${randomHour}:${randomMinute}`);
  // }
}
