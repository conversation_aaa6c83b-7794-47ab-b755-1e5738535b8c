import {
  <PERSON>,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  Request,
  UnauthorizedException,
  ParseIntPipe,
  Logger,
} from '@nestjs/common';
import { SubscriptionsService } from './subscriptions.service';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { CancelSubscriptionDto } from './dto/cancel-subscription.dto';
import { RenewSubscriptionDto } from './dto/renew-subscription.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { SubscriptionEmailService } from './services/subscription-email.service';
import { UsersService } from '../users/users.service';

@Controller('subscriptions')
@UseGuards(JwtAuthGuard)
export class SubscriptionsController {
  private readonly logger = new Logger(SubscriptionsController.name);

  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    private readonly subscriptionEmailService: SubscriptionEmailService,
    private readonly usersService: UsersService,
  ) {}

  @Post('create')
  async createSubscription(
    @Request() req: { user: { sub: number } },
    @Body() createSubscriptionDto: CreateSubscriptionDto,
  ) {
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not authenticated');
    }
    createSubscriptionDto.user_id = req.user.sub;
    return this.subscriptionsService.createSubscription(createSubscriptionDto);
  }

  @Post('activate/:paypalSubscriptionId')
  async activateSubscription(
    @Param('paypalSubscriptionId') paypalSubscriptionId: string,
  ) {
    return this.subscriptionsService.activateSubscription(
      '', // We'll find by paypal subscription ID
      paypalSubscriptionId,
    );
  }

  @Post('cancel/:id')
  async cancelSubscription(
    @Param('id', ParseIntPipe) id: number,
    @Body() cancelDto: CancelSubscriptionDto,
    @Request() req: { user: { sub: number } },
  ) {
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Verify the subscription belongs to the user
    const subscription =
      await this.subscriptionsService.getSubscriptionStatus(id);
    if (subscription.user_id !== req.user.sub) {
      throw new UnauthorizedException('Access denied');
    }

    return this.subscriptionsService.cancelSubscription(id, cancelDto);
  }

  @Get('user')
  async getUserSubscriptions(@Request() req: { user: { sub: number } }) {
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not authenticated');
    }
    return this.subscriptionsService.getUserSubscriptions(req.user.sub);
  }

  @Get(':id')
  async getSubscription(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: { user: { sub: number } },
  ) {
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not authenticated');
    }

    const subscription =
      await this.subscriptionsService.getSubscriptionStatus(id);

    // Verify the subscription belongs to the user
    if (subscription.user_id !== req.user.sub) {
      throw new UnauthorizedException('Access denied');
    }

    return subscription;
  }

  @Post('renew/:id')
  async renewSubscription(
    @Param('id', ParseIntPipe) id: number,
    @Body() renewDto: RenewSubscriptionDto,
    @Request() req: { user: { sub: number } },
  ) {
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Verify the subscription belongs to the user
    const subscription =
      await this.subscriptionsService.getSubscriptionStatus(id);
    if (subscription.user_id !== req.user.sub) {
      throw new UnauthorizedException('Access denied');
    }

    return this.subscriptionsService.renewSubscription(
      id,
      renewDto.package_duration_id,
    );
  }

  @Post('reactivate/:id')
  async reactivateExpiredSubscription(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: { user: { sub: number } },
  ) {
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Verify the subscription belongs to the user
    const subscription =
      await this.subscriptionsService.getSubscriptionStatus(id);
    if (subscription.user_id !== req.user.sub) {
      throw new UnauthorizedException('Access denied');
    }

    return this.subscriptionsService.reactivateExpiredSubscription(id);
  }

  @Get('check-expired')
  async checkExpiredSubscriptions() {
    // This endpoint can be used for manual checks or by admin
    return this.subscriptionsService.checkExpiredSubscriptions();
  }
}
