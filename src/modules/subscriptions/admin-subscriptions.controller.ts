import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subscription } from './entities/subscription.entity';
import { SubscriptionsService } from './subscriptions.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CancelSubscriptionDto } from './dto/cancel-subscription.dto';

@Controller('admin/subscriptions')
@UseGuards(JwtAuthGuard)
export class AdminSubscriptionsController {
  constructor(
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    private subscriptionsService: SubscriptionsService,
  ) {}

  @Get()
  async getAllSubscriptions(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('status') status?: string,
    @Query('search') search?: string,
  ) {
    const queryBuilder = this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .leftJoinAndSelect('subscription.packageDuration', 'packageDuration')
      .leftJoinAndSelect('packageDuration.package', 'package');

    // Filter by status if provided
    if (status) {
      queryBuilder.andWhere('subscription.status = :status', { status });
    }

    // Search by user email or package name
    if (search) {
      queryBuilder.andWhere(
        '(user.email ILIKE :search OR package.name ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Order by creation date (newest first)
    queryBuilder.orderBy('subscription.created_at', 'DESC');

    const [subscriptions, total] = await queryBuilder.getManyAndCount();

    return {
      data: subscriptions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  @Get('stats')
  async getSubscriptionStats() {
    const stats = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .select([
        'COUNT(*) as total_subscriptions',
        "COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscriptions",
        "COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_subscriptions",
        "COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_subscriptions",
        "COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_subscriptions",
        "SUM(CASE WHEN status = 'active' THEN amount ELSE 0 END) as monthly_revenue",
      ])
      .getRawOne();

    // Get subscription growth over the last 12 months
    const growthStats = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .select([
        "DATE_TRUNC('month', created_at) as month",
        'COUNT(*) as new_subscriptions',
        'SUM(amount) as revenue',
      ])
      .where("created_at >= NOW() - INTERVAL '12 months'")
      .groupBy("DATE_TRUNC('month', created_at)")
      .orderBy('month', 'ASC')
      .getRawMany();

    return {
      overview: {
        totalSubscriptions: parseInt(stats.total_subscriptions),
        activeSubscriptions: parseInt(stats.active_subscriptions),
        cancelledSubscriptions: parseInt(stats.cancelled_subscriptions),
        suspendedSubscriptions: parseInt(stats.suspended_subscriptions),
        expiredSubscriptions: parseInt(stats.expired_subscriptions),
        monthlyRevenue: parseFloat(stats.monthly_revenue) || 0,
      },
      growth: growthStats.map((stat) => ({
        month: stat.month,
        newSubscriptions: parseInt(stat.new_subscriptions),
        revenue: parseFloat(stat.revenue),
      })),
    };
  }

  @Get('revenue')
  async getRevenueStats(
    @Query('period', new DefaultValuePipe('month'))
    period: 'day' | 'week' | 'month' | 'year',
  ) {
    let dateFormat: string;
    let interval: string;

    switch (period) {
      case 'day':
        dateFormat = 'day';
        interval = '30 days';
        break;
      case 'week':
        dateFormat = 'week';
        interval = '12 weeks';
        break;
      case 'year':
        dateFormat = 'year';
        interval = '5 years';
        break;
      default:
        dateFormat = 'month';
        interval = '12 months';
    }

    const revenueData = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .select([
        `DATE_TRUNC('${dateFormat}', created_at) as period`,
        'SUM(amount) as revenue',
        'COUNT(*) as subscription_count',
      ])
      .where(`created_at >= NOW() - INTERVAL '${interval}'`)
      .andWhere('status IN (:...statuses)', {
        statuses: ['active', 'cancelled'],
      })
      .groupBy(`DATE_TRUNC('${dateFormat}', created_at)`)
      .orderBy('period', 'ASC')
      .getRawMany();

    return revenueData.map((data) => ({
      period: data.period,
      revenue: parseFloat(data.revenue),
      subscriptionCount: parseInt(data.subscription_count),
    }));
  }

  @Get(':id')
  async getSubscription(@Param('id', ParseIntPipe) id: number) {
    const subscription = await this.subscriptionRepository.findOne({
      where: { id },
      relations: ['user', 'packageDuration', 'packageDuration.package'],
    });

    if (!subscription) {
      throw new Error('Subscription not found');
    }

    return subscription;
  }

  @Post(':id/cancel')
  async adminCancelSubscription(
    @Param('id', ParseIntPipe) id: number,
    @Body() cancelDto: CancelSubscriptionDto,
  ) {
    return this.subscriptionsService.cancelSubscription(id, {
      reason: cancelDto.reason || 'Cancelled by administrator',
    });
  }

  @Get('users/:userId')
  async getUserSubscriptions(@Param('userId', ParseIntPipe) userId: number) {
    return this.subscriptionsService.getUserSubscriptions(userId);
  }

  @Get('packages/popular')
  async getPopularPackages() {
    const popularPackages = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.packageDuration', 'packageDuration')
      .leftJoinAndSelect('packageDuration.package', 'package')
      .select([
        'package.id',
        'package.name',
        'COUNT(subscription.id) as subscription_count',
        'SUM(subscription.amount) as total_revenue',
        'AVG(subscription.amount) as avg_amount',
      ])
      .where('subscription.status IN (:...statuses)', {
        statuses: ['active', 'cancelled'],
      })
      .groupBy('package.id, package.name')
      .orderBy('subscription_count', 'DESC')
      .limit(10)
      .getRawMany();

    return popularPackages.map((pkg) => ({
      packageId: pkg.package_id,
      packageName: pkg.package_name,
      subscriptionCount: parseInt(pkg.subscription_count),
      totalRevenue: parseFloat(pkg.total_revenue),
      averageAmount: parseFloat(pkg.avg_amount),
    }));
  }

  @Get('churn/analysis')
  async getChurnAnalysis() {
    // Calculate churn rate over the last 12 months
    const churnData = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .select([
        "DATE_TRUNC('month', cancelled_at) as month",
        'COUNT(*) as cancelled_count',
      ])
      .where("cancelled_at >= NOW() - INTERVAL '12 months'")
      .andWhere('cancelled_at IS NOT NULL')
      .groupBy("DATE_TRUNC('month', cancelled_at)")
      .orderBy('month', 'ASC')
      .getRawMany();

    // Get total active subscriptions at the beginning of each month
    const activeData = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .select([
        "DATE_TRUNC('month', created_at) as month",
        'COUNT(*) as active_count',
      ])
      .where("created_at >= NOW() - INTERVAL '12 months'")
      .andWhere("status = 'active'")
      .groupBy("DATE_TRUNC('month', created_at)")
      .orderBy('month', 'ASC')
      .getRawMany();

    // Calculate churn rate for each month
    const churnAnalysis = churnData.map((churn) => {
      const activeInMonth = activeData.find(
        (active) =>
          new Date(active.month).getTime() === new Date(churn.month).getTime(),
      );

      const churnRate = activeInMonth
        ? (parseInt(churn.cancelled_count) /
            parseInt(activeInMonth.active_count)) *
          100
        : 0;

      return {
        month: churn.month,
        cancelledCount: parseInt(churn.cancelled_count),
        activeCount: activeInMonth ? parseInt(activeInMonth.active_count) : 0,
        churnRate: Math.round(churnRate * 100) / 100, // Round to 2 decimal places
      };
    });

    return churnAnalysis;
  }

  @Get('export/csv')
  async exportSubscriptionsCSV(
    @Query('status') status?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const queryBuilder = this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .leftJoinAndSelect('subscription.packageDuration', 'packageDuration')
      .leftJoinAndSelect('packageDuration.package', 'package');

    if (status) {
      queryBuilder.andWhere('subscription.status = :status', { status });
    }

    if (startDate) {
      queryBuilder.andWhere('subscription.created_at >= :startDate', {
        startDate,
      });
    }

    if (endDate) {
      queryBuilder.andWhere('subscription.created_at <= :endDate', { endDate });
    }

    const subscriptions = await queryBuilder.getMany();

    // Convert to CSV format
    const csvData = subscriptions.map((sub) => ({
      id: sub.id,
      user_email: sub.user?.email,
      package_name: sub.packageDuration?.package?.name,
      amount: sub.amount,
      currency: sub.currency,
      status: sub.status,
      start_date: sub.start_date,
      next_billing_date: sub.next_billing_date,
      cancelled_at: sub.cancelled_at,
      created_at: sub.created_at,
    }));

    return {
      data: csvData,
      filename: `subscriptions_export_${new Date().toISOString().split('T')[0]}.csv`,
    };
  }
}
