/**
 * Pricing utilities for consistent discount calculations across the application
 */

export interface PricingInfo {
  originalPrice: number;
  discountedPrice: number;
  discountPercent: number;
  discountAmount: number;
}

/**
 * Calculate discounted price using the standard formula:
 * discountedPrice = originalPrice * (1 - discountPercent / 100)
 *
 * @param originalPrice - The original price before discount
 * @param discountPercent - The discount percentage (0-100)
 * @returns The discounted price
 */
export function calculateDiscountedPrice(
  originalPrice: number,
  discountPercent: number = 0,
): number {
  if (discountPercent < 0 || discountPercent > 100) {
    throw new Error('Discount percent must be between 0 and 100');
  }

  return originalPrice * (1 - discountPercent / 100);
}

/**
 * Calculate comprehensive pricing information including original price,
 * discounted price, discount amount, and discount percentage
 *
 * @param originalPrice - The original price before discount
 * @param discountPercent - The discount percentage (0-100)
 * @returns Complete pricing information
 */
export function calculatePricingInfo(
  originalPrice: number,
  discountPercent: number = 0,
): PricingInfo {
  if (discountPercent < 0 || discountPercent > 100) {
    throw new Error('Discount percent must be between 0 and 100');
  }

  const discountedPrice = calculateDiscountedPrice(
    originalPrice,
    discountPercent,
  );
  const discountAmount = originalPrice - discountedPrice;

  return {
    originalPrice: Number(originalPrice.toFixed(2)),
    discountedPrice: Number(discountedPrice.toFixed(2)),
    discountPercent,
    discountAmount: Number(discountAmount.toFixed(2)),
  };
}

/**
 * Format price for display with currency
 *
 * @param price - The price to format
 * @param currency - The currency code (default: 'USD')
 * @param locale - The locale for formatting (default: 'en-US')
 * @returns Formatted price string
 */
export function formatPrice(
  price: number,
  currency: string = 'USD',
  locale: string = 'en-US',
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(price);
}

/**
 * Calculate price with currency conversion
 *
 * @param priceUSD - Price in USD
 * @param targetCurrency - Target currency code
 * @param exchangeRate - Exchange rate from USD to target currency
 * @returns Converted price
 */
export function convertCurrency(
  priceUSD: number,
  targetCurrency: string,
  exchangeRate: number = 1,
): number {
  if (targetCurrency === 'USD') {
    return priceUSD;
  }

  // For VND conversion (example rate: 1 USD = 25,000 VND)
  if (targetCurrency === 'VND') {
    return priceUSD * (exchangeRate || 25000);
  }

  return priceUSD * exchangeRate;
}

/**
 * Validate pricing data
 *
 * @param price - The price to validate
 * @param discountPercent - The discount percentage to validate
 * @throws Error if validation fails
 */
export function validatePricing(
  price: number,
  discountPercent: number = 0,
): void {
  if (price < 0) {
    throw new Error('Price cannot be negative');
  }

  if (discountPercent < 0 || discountPercent > 100) {
    throw new Error('Discount percent must be between 0 and 100');
  }
}
