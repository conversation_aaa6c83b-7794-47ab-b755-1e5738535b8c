import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('cookies')
export class Cookie {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  website_url: string;

  @Column()
  encrypted_cookie: string;

  @ManyToMany(() => User)
  @JoinTable({
    name: 'cookie_allowed_members',
    joinColumn: {
      name: 'cookieId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'userId',
      referencedColumnName: 'id',
    },
  })
  allowed_members: User[];

  @CreateDateColumn()
  last_updated: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
