import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CookiesController } from './cookies.controller';
import { CookiesService } from './cookies.service';
import { User } from '../users/entities/user.entity';
import { AccountsModule } from '../accounts/accounts.module';
import { Account } from '../accounts/entities/account.entity';
import { ServerCookie } from '../accounts/entities/server-cookie.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Account, ServerCookie]),
    AccountsModule,
  ],
  controllers: [CookiesController],
  providers: [CookiesService],
  exports: [CookiesService],
})
export class CookiesModule {}
