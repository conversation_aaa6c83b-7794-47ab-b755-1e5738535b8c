import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Prompt } from './entities/prompt.entity';
import { PromptCategory } from './entities/prompt-category.entity';
import { Topic } from './entities/topic.entity';
import { PromptsController } from './prompts.controller';
import { PromptsService } from './prompts.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Prompt, PromptCategory, Topic]),
    AuthModule,
  ],
  controllers: [PromptsController],
  providers: [PromptsService],
  exports: [PromptsService],
})
export class PromptsModule {}
