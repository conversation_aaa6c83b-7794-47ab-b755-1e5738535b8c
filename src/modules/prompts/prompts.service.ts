import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Prompt } from './entities/prompt.entity';
import { PromptCategory } from './entities/prompt-category.entity';
import { Topic } from './entities/topic.entity';
import { CreatePromptDto } from './dto/create-prompt.dto';
import { CreatePromptCategoryDto } from './dto/create-prompt-category.dto';
import { QueryPromptsDto } from './dto/query-prompts.dto';

@Injectable()
export class PromptsService {
  constructor(
    @InjectRepository(Prompt)
    private promptRepository: Repository<Prompt>,
    @InjectRepository(PromptCategory)
    private promptCategoryRepository: Repository<PromptCategory>,
    @InjectRepository(Topic)
    private topicRepository: Repository<Topic>,
  ) {}

  // Prompt Categories
  async findAllCategories(): Promise<PromptCategory[]> {
    return this.promptCategoryRepository.find({
      order: { created_at: 'DESC' },
    });
  }

  async createCategory(createPromptCategoryDto: CreatePromptCategoryDto): Promise<PromptCategory> {
    const category = this.promptCategoryRepository.create(createPromptCategoryDto);
    return this.promptCategoryRepository.save(category);
  }

  async findCategoryById(id: number): Promise<PromptCategory> {
    const category = await this.promptCategoryRepository.findOne({
      where: { id },
      relations: ['prompts'],
    });
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
    return category;
  }

  // Prompts
  async findPromptsByCategory(queryDto: QueryPromptsDto) {
    const { page = 1, pageSize = 12, category_id, search_text } = queryDto;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.promptRepository
      .createQueryBuilder('prompt')
      .leftJoinAndSelect('prompt.category', 'category')
      .leftJoinAndSelect('prompt.topic', 'topic');

    if (category_id) {
      queryBuilder.where('prompt.category_id = :category_id', { category_id });
    }

    if (search_text && search_text.trim()) {
      queryBuilder.andWhere(
        '(prompt.title ILIKE :search OR prompt.short_description ILIKE :search OR prompt.content ILIKE :search)',
        { search: `%${search_text.trim()}%` }
      );
    }

    const [prompts, total] = await queryBuilder
      .orderBy('prompt.created_at', 'DESC')
      .skip(skip)
      .take(pageSize)
      .getManyAndCount();

    return {
      data: prompts,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    };
  }

  async findPromptById(id: number): Promise<Prompt> {
    const prompt = await this.promptRepository.findOne({
      where: { id },
      relations: ['category', 'topic'],
    });

    if (!prompt) {
      throw new NotFoundException(`Prompt with ID ${id} not found`);
    }

    // Increment view count
    await this.promptRepository.update(id, {
      view_count: prompt.view_count + 1,
    });

    return { ...prompt, view_count: prompt.view_count + 1 };
  }

  async createPrompt(createPromptDto: CreatePromptDto): Promise<Prompt> {
    const prompt = this.promptRepository.create(createPromptDto);
    return this.promptRepository.save(prompt);
  }

  async findAllPrompts(): Promise<Prompt[]> {
    return this.promptRepository.find({
      relations: ['category', 'topic'],
      order: { created_at: 'DESC' },
    });
  }

  // Topics
  async findAllTopics(): Promise<Topic[]> {
    return this.topicRepository.find({
      order: { created_at: 'DESC' },
    });
  }
}
