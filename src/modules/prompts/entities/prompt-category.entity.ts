import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Prompt } from './prompt.entity';

@Entity('prompt_categories')
export class PromptCategory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ length: 500, nullable: true })
  image_url: string;

  @Column({ length: 500, nullable: true })
  image_card_url: string;

  @Column({ type: 'integer', default: 0 })
  prompt_count: number;

  @Column({ type: 'boolean', default: false })
  is_coming_soon: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @OneToMany(() => Prompt, (prompt) => prompt.category)
  prompts: Prompt[];
}
