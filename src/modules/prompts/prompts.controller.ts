import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { PromptsService } from './prompts.service';
import { CreatePromptDto } from './dto/create-prompt.dto';
import { CreatePromptCategoryDto } from './dto/create-prompt-category.dto';
import { QueryPromptsDto } from './dto/query-prompts.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

@Controller()
export class PromptsController {
  constructor(private readonly promptsService: PromptsService) {}

  // GET /prompt-categories - Get danh sách categories
  @Get('prompt-categories')
  async getPromptCategories() {
    try {
      return await this.promptsService.findAllCategories();
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve prompt categories: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // GET /prompts/by-category?page=1&pageSize=12&category_id=16&search_text= - Get prompt list by category id
  @Get('prompts/by-category')
  async getPromptsByCategory(@Query() queryDto: QueryPromptsDto) {
    try {
      return await this.promptsService.findPromptsByCategory(queryDto);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve prompts by category: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // GET /prompts/:id - Get prompt detail
  @Get('prompts/:id')
  async getPromptById(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.promptsService.findPromptById(id);
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        'Failed to retrieve prompt: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Admin routes (protected)
  @Post('admin/prompt-categories')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async createPromptCategory(@Body() createPromptCategoryDto: CreatePromptCategoryDto) {
    try {
      return await this.promptsService.createCategory(createPromptCategoryDto);
    } catch (error) {
      throw new HttpException(
        'Failed to create prompt category: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('admin/prompts')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async createPrompt(@Body() createPromptDto: CreatePromptDto) {
    try {
      return await this.promptsService.createPrompt(createPromptDto);
    } catch (error) {
      throw new HttpException(
        'Failed to create prompt: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('admin/prompts')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async getAllPrompts() {
    try {
      return await this.promptsService.findAllPrompts();
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve all prompts: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('topics')
  async getTopics() {
    try {
      return await this.promptsService.findAllTopics();
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve topics: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
