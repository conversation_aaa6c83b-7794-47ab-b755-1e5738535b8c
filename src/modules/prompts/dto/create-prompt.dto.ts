import { IsString, IsOptional, IsNumber } from 'class-validator';

export class CreatePromptDto {
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  short_description?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  prompt_text?: string;

  @IsOptional()
  @IsString()
  optimization_guide?: string;

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsNumber()
  topic_id?: number;

  @IsOptional()
  @IsNumber()
  is_type?: number;

  @IsOptional()
  @IsNumber()
  sub_type?: number;

  @IsOptional()
  @IsString()
  what_field?: string;

  @IsOptional()
  @IsString()
  tips_field?: string;

  @IsOptional()
  @IsString()
  how_field?: string;

  @IsOptional()
  @IsString()
  input_field?: string;

  @IsOptional()
  @IsString()
  output_field?: string;

  @IsOptional()
  @IsString()
  add_tip?: string;

  @IsOptional()
  @IsString()
  additional_information?: string;

  @IsOptional()
  @IsNumber()
  view_count?: number;
}
