import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ExpirationCheckService } from './expiration-check.service';
import { User } from '../users/entities/user.entity';
import { PackageUser } from '../packages/entities/package-user.entity';
import { UsersModule } from '../users/users.module';
import { DiscordModule } from '../discord/discord.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([User, PackageUser]),
    UsersModule,
    DiscordModule,
  ],
  providers: [ExpirationCheckService],
  exports: [ExpirationCheckService],
})
export class SchedulerModule {}
