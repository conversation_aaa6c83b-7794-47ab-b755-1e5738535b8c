import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateTrialUserDto } from './dto/create-trial-user.dto';
import * as bcrypt from 'bcrypt';
import { PackageUser } from '../packages/entities/package-user.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    @InjectRepository(PackageUser)
    private readonly packageUserRepository: Repository<PackageUser>,
  ) {}

  async findById(id: number): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async updateStatus(id: number, status: string): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    user.status = status;
    return this.usersRepository.save(user);
  }

  async createTrialUser(createTrialUserDto: CreateTrialUserDto): Promise<User> {
    // Check if user with this email already exists
    const existingUser = await this.usersRepository.findOne({
      where: { email: createTrialUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Use provided password or generate a random one
    const password =
      createTrialUserDto.password || Math.random().toString(36).slice(-8);
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the trial user
    const user = this.usersRepository.create({
      email: createTrialUserDto.email,
      password: hashedPassword,
      plain_password: password, // Store the plain password
      status: 'trial',
      role: 'user',
    });

    const savedUser = await this.usersRepository.save(user);

    // Assign user to the selected package
    const packageUser = this.packageUserRepository.create({
      user_id: savedUser.id,
      package_id: createTrialUserDto.package_id,
      start_date: new Date(),
      expires_at: new Date(
        Date.now() + createTrialUserDto.trial_days * 24 * 60 * 60 * 1000,
      ),
      status: 'active',
    });

    await this.packageUserRepository.save(packageUser);

    // Return the user with the plain text password
    return {
      ...savedUser,
    };
  }

  async findAllTrialUsers(page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [users, total] = await this.usersRepository.findAndCount({
      where: { status: 'trial' },
      relations: ['packageUsers'],
      order: { created_at: 'DESC' },
      skip,
      take: limit,
    });

    return {
      data: users,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async removeUser(id: number): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    await this.usersRepository.remove(user);
  }

  async findTrialUserById(id: number) {
    const user = await this.usersRepository.findOne({
      where: { id, status: 'trial' },
      relations: ['packageUsers', 'packageUsers.package'],
      select: [
        'id',
        'email',
        'password',
        'plain_password',
        'status',
        'role',
        'created_at',
        'updated_at',
      ],
    });

    if (!user) {
      throw new NotFoundException(`Trial user with ID ${id} not found`);
    }

    // Rename package property to packageInfo to avoid EJS reserved keyword issues
    if (user.packageUsers && user.packageUsers.length > 0) {
      user.packageUsers.forEach((pu: any) => {
        if (pu.package) {
          pu.packageInfo = pu.package;
          delete pu.package;
        }
      });
    }

    return user;
  }
}
