import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInviteLinksTable1710429000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "invite_links" (
        "id" SERIAL PRIMARY KEY,
        "invite_code" VARCHAR(255) NOT NULL UNIQUE,
        "discord_user_id" VARCHAR(255) NOT NULL,
        "role_id" VARCHAR(255) NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "expires_at" TIMESTAMP NOT NULL,
        "used" BOOLEAN NOT NULL DEFAULT false,
        CONSTRAINT "fk_discord_user"
          FOREIGN KEY ("discord_user_id") 
          REFERENCES "users"("discord_id") 
          ON DELETE CASCADE
      )
    `);

    // Create index for faster lookups
    await queryRunner.query(`
      CREATE INDEX "idx_invite_code" ON "invite_links"("invite_code");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE IF EXISTS "invite_links" CASCADE');
  }
}
