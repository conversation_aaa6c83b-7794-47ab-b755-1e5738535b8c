import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCookiesTable1714500000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create Cookies table
    await queryRunner.query(`
      CREATE TABLE "cookies" (
        "id" SERIAL PRIMARY KEY,
        "website_url" VARCHAR(255) NOT NULL,
        "encrypted_cookie" TEXT NOT NULL,
        "last_updated" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // Create join table for many-to-many relationship with users
    await queryRunner.query(`
      CREATE TABLE "cookie_allowed_members" (
        "cookieId" INTEGER NOT NULL,
        "userId" INTEGER NOT NULL,
        PRIMARY KEY ("cookieId", "userId"),
        FOREIGN KEY ("cookieId") REFERENCES "cookies"("id") ON DELETE CASCADE,
        <PERSON><PERSON><PERSON><PERSON><PERSON> KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'DROP TABLE IF EXISTS "cookie_allowed_members" CASCADE',
    );
    await queryRunner.query('DROP TABLE IF EXISTS "cookies" CASCADE');
  }
}
