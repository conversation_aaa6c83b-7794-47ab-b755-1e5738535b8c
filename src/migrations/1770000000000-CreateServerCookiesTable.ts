import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateServerCookiesTable1770000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create server_cookies table
    await queryRunner.query(`
      CREATE TABLE "server_cookies" (
        "id" SERIAL PRIMARY KEY,
        "serverId" INTEGER NOT NULL,
        "accountId" INTEGER NOT NULL,
        "cookieData" TEXT,
        "lastUsed" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "FK_server_cookies_account"
          FOREIGN KEY ("accountId")
          REFERENCES "accounts"("id")
          ON DELETE CASCADE
      )
    `);

    // Create indexes for faster lookups
    await queryRunner.query(`
      CREATE INDEX "idx_server_cookies_account_id" ON "server_cookies"("accountId");
      CREATE INDEX "idx_server_cookies_server_id" ON "server_cookies"("serverId");
    `);

    // Migrate existing cookie data to server_cookies table with serverId = 1 (default)
    await queryRunner.query(`
      INSERT INTO "server_cookies" ("accountId", "serverId", "cookieData", "createdAt", "updatedAt")
      SELECT "id", 1, "cookie_data", "created_at", "updated_at"
      FROM "accounts"
      WHERE "cookie_data" IS NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the server_cookies table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "server_cookies" CASCADE
    `);
  }
}
