import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSortColumnToAccounts1820000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add sort column to accounts table
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      ADD COLUMN "sort" INTEGER DEFAULT 0 NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove sort column from accounts table
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      DROP COLUMN "sort"
    `);
  }
}
