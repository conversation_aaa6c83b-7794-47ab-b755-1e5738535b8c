import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDescriptionToAccounts1790000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add description column to accounts table
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      ADD COLUMN "description" TEXT DEFAULT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove description column from accounts table
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      DROP COLUMN "description"
    `);
  }
}
