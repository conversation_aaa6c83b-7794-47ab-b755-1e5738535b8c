import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePromptTables1840000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create prompt_categories table
    await queryRunner.query(`
      CREATE TABLE "prompt_categories" (
        "id" SERIAL PRIMARY KEY,
        "name" VARCHAR(255) NOT NULL,
        "description" TEXT,
        "image_url" VARCHAR(500),
        "image_card_url" VARCHAR(500),
        "prompt_count" INTEGER DEFAULT 0,
        "is_coming_soon" BOOLEAN DEFAULT FALSE,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // Create topics table
    await queryRunner.query(`
      CREATE TABLE "topics" (
        "id" SERIAL PRIMARY KEY,
        "name" VARCHAR(255) NOT NULL,
        "description" TEXT,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // Create prompts table
    await queryRunner.query(`
      CREATE TABLE "prompts" (
        "id" SERIAL PRIMARY KEY,
        "title" VARCHAR(500) NOT NULL,
        "short_description" TEXT,
        "content" TEXT,
        "prompt_text" TEXT,
        "optimization_guide" TEXT,
        "category_id" INTEGER,
        "topic_id" INTEGER,
        "is_type" SMALLINT DEFAULT 1,
        "sub_type" SMALLINT,
        "what_field" TEXT,
        "tips_field" TEXT,
        "how_field" TEXT,
        "input_field" TEXT,
        "output_field" TEXT,
        "add_tip" TEXT,
        "additional_information" TEXT,
        "view_count" INTEGER DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "fk_prompts_category"
          FOREIGN KEY ("category_id") 
          REFERENCES "prompt_categories"("id") 
          ON DELETE SET NULL,
        CONSTRAINT "fk_prompts_topic"
          FOREIGN KEY ("topic_id") 
          REFERENCES "topics"("id") 
          ON DELETE SET NULL
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "idx_prompts_category_id" ON "prompts"("category_id");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_prompts_topic_id" ON "prompts"("topic_id");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_prompts_title" ON "prompts"("title");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_prompt_categories_name" ON "prompt_categories"("name");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_topics_name" ON "topics"("name");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_topics_name"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_prompt_categories_name"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_prompts_title"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_prompts_topic_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_prompts_category_id"`);

    // Drop tables in reverse order
    await queryRunner.query(`DROP TABLE IF EXISTS "prompts"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "topics"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "prompt_categories"`);
  }
}
