import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionTable1830000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create subscriptions table
    await queryRunner.query(`
      CREATE TABLE "subscriptions" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL,
        "paypal_subscription_id" VARCHAR(255) NOT NULL UNIQUE,
        "paypal_plan_id" VARCHAR(255) NOT NULL,
        "package_duration_id" INTEGER,
        "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
        "start_date" TIMESTAMP NOT NULL,
        "next_billing_date" TIMESTAMP,
        "amount" DECIMAL(10,2) NOT NULL,
        "currency" VARCHAR(3) NOT NULL DEFAULT 'USD',
        "cancelled_at" TIMESTAMP,
        "cancel_reason" TEXT,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        <PERSON><PERSON><PERSON><PERSON><PERSON> KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE,
        FOREIGN KEY ("package_duration_id") REFERENCES "package_durations"("id") ON DELETE SET NULL
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "IDX_subscriptions_user_id" ON "subscriptions" ("user_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_subscriptions_paypal_subscription_id" ON "subscriptions" ("paypal_subscription_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_subscriptions_status" ON "subscriptions" ("status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_subscriptions_next_billing_date" ON "subscriptions" ("next_billing_date")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_subscriptions_next_billing_date"`);
    await queryRunner.query(`DROP INDEX "IDX_subscriptions_status"`);
    await queryRunner.query(
      `DROP INDEX "IDX_subscriptions_paypal_subscription_id"`,
    );
    await queryRunner.query(`DROP INDEX "IDX_subscriptions_user_id"`);

    // Drop table
    await queryRunner.query(`DROP TABLE "subscriptions"`);
  }
}
