import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedPromptData1840000100000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Insert sample prompt categories
    await queryRunner.query(`
      INSERT INTO prompt_categories (name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at)
      VALUES 
        ('Marketing', 'Marketing and advertising prompts', 'https://example.com/marketing.jpg', 'https://example.com/marketing-card.jpg', 0, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Content Writing', 'Content creation and writing prompts', 'https://example.com/content.jpg', 'https://example.com/content-card.jpg', 0, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Business', 'Business strategy and planning prompts', 'https://example.com/business.jpg', 'https://example.com/business-card.jpg', 0, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Education', 'Educational and learning prompts', 'https://example.com/education.jpg', 'https://example.com/education-card.jpg', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Technology', 'Technology and programming prompts', 'https://example.com/tech.jpg', 'https://example.com/tech-card.jpg', 0, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
    `);

    // Insert sample topics
    await queryRunner.query(`
      INSERT INTO topics (name, description, created_at, updated_at)
      VALUES 
        ('Social Media', 'Social media marketing and content', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Email Marketing', 'Email campaigns and newsletters', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Blog Writing', 'Blog posts and articles', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('SEO', 'Search engine optimization', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Product Description', 'Product descriptions and features', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Business Plan', 'Business planning and strategy', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Web Development', 'Web development and programming', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Data Analysis', 'Data analysis and insights', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
    `);

    // Insert sample prompts
    await queryRunner.query(`
      INSERT INTO prompts (title, short_description, content, prompt_text, category_id, topic_id, is_type, view_count, created_at, updated_at)
      VALUES 
        (
          'Social Media Post Generator',
          'Generate engaging social media posts for any platform',
          'This prompt helps you create compelling social media content that drives engagement and reaches your target audience.',
          'Create a social media post for [PLATFORM] about [TOPIC]. The post should be [TONE] and include relevant hashtags. Target audience: [AUDIENCE]',
          1,
          1,
          1,
          0,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        ),
        (
          'Email Subject Line Creator',
          'Create compelling email subject lines that increase open rates',
          'Generate attention-grabbing email subject lines that encourage recipients to open and read your emails.',
          'Write 5 compelling email subject lines for [EMAIL_TYPE] targeting [AUDIENCE]. The subject lines should be [TONE] and create urgency/curiosity.',
          1,
          2,
          1,
          0,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        ),
        (
          'Blog Post Outline Generator',
          'Create detailed outlines for blog posts on any topic',
          'Generate comprehensive blog post outlines that structure your content for maximum readability and SEO impact.',
          'Create a detailed blog post outline for the topic: [TOPIC]. Include an introduction, 5-7 main sections with subpoints, and a conclusion. Target audience: [AUDIENCE]',
          2,
          3,
          1,
          0,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        ),
        (
          'Product Description Writer',
          'Write compelling product descriptions that convert',
          'Create persuasive product descriptions that highlight benefits and encourage purchases.',
          'Write a compelling product description for [PRODUCT_NAME]. Highlight key features, benefits, and include a call-to-action. Target customer: [TARGET_CUSTOMER]',
          2,
          5,
          1,
          0,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        ),
        (
          'Business Plan Executive Summary',
          'Create executive summaries for business plans',
          'Generate professional executive summaries that capture the essence of your business plan.',
          'Write an executive summary for a business plan for [BUSINESS_TYPE]. Include company overview, market opportunity, competitive advantage, and financial projections.',
          3,
          6,
          1,
          0,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        );
    `);

    // Update prompt counts in categories
    await queryRunner.query(`
      UPDATE prompt_categories 
      SET prompt_count = (
        SELECT COUNT(*) 
        FROM prompts 
        WHERE prompts.category_id = prompt_categories.id
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM prompts;`);
    await queryRunner.query(`DELETE FROM topics;`);
    await queryRunner.query(`DELETE FROM prompt_categories;`);
  }
}
