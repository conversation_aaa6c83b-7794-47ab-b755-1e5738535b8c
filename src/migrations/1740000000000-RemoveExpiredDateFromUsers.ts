import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveExpiredDateFromUsers1740000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove expired_date column from users table
    await queryRunner.query(`
      ALTER TABLE "users" DROP COLUMN IF EXISTS "expired_date"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back expired_date column if needed
    await queryRunner.query(`
      ALTER TABLE "users" ADD COLUMN "expired_date" TIMESTAMP NULL
    `);
  }
}
