import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPackagesAndUpdateAccounts1720000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create Packages table
    await queryRunner.query(`
      CREATE TABLE "packages" (
        "id" SERIAL PRIMARY KEY,
        "name" VARCHAR(255) NOT NULL,
        "description" TEXT,
        "price" DECIMAL(10,2) NOT NULL,
        "duration_days" INTEGER NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // Create join table for many-to-many relationship between accounts and packages
    await queryRunner.query(`
      CREATE TABLE "account_packages" (
        "account_id" INTEGER NOT NULL,
        "package_id" INTEGER NOT NULL,
        "assigned_at" TIMESTAMP NOT NULL DEFAULT now(),
        "expires_at" TIMESTAMP NOT NULL,
        PRIMARY KEY ("account_id", "package_id"),
        FOREIGN KEY ("account_id") REFERENCES "accounts"("id") ON DELETE CASCADE,
        FOREIGN KEY ("package_id") REFERENCES "packages"("id") ON DELETE CASCADE
      )
    `);

    // Modify Accounts table
    // 1. Make username and password nullable
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      ALTER COLUMN "username" DROP NOT NULL,
      ALTER COLUMN "password" DROP NOT NULL
    `);

    // 2. Add login_cookie column with default value 1
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      ADD COLUMN "login_cookie" INTEGER NOT NULL DEFAULT 1
    `);

    // 3. Add name column
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      ADD COLUMN "name" VARCHAR(255)
    `);

    // 4. Add img_intro column
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      ADD COLUMN "img_intro" VARCHAR(255)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the new columns from accounts table
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      DROP COLUMN "img_intro",
      DROP COLUMN "name",
      DROP COLUMN "login_cookie"
    `);

    // Make username and password required again
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      ALTER COLUMN "username" SET NOT NULL,
      ALTER COLUMN "password" SET NOT NULL
    `);

    // Drop the join table
    await queryRunner.query('DROP TABLE IF EXISTS "account_packages" CASCADE');

    // Drop the packages table
    await queryRunner.query('DROP TABLE IF EXISTS "packages" CASCADE');
  }
}
