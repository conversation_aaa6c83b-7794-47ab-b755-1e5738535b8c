import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFeaturesColumnToPackages1760000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add features column as an array of strings (nullable)
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "features" TEXT[] DEFAULT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove features column
    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN "features"
    `);
  }
}
