import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPaymentDateColumn1710428927000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "payments"
      ADD COLUMN "payment_date" TIMESTAMP
    `);

    // Update existing records to set payment_date to created_at
    await queryRunner.query(`
      UPDATE "payments"
      SET "payment_date" = "created_at"
      WHERE "payment_date" IS NULL
    `);

    // Make payment_date not nullable
    await queryRunner.query(`
      ALTER TABLE "payments"
      ALTER COLUMN "payment_date" SET NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "payments"
      DROP COLUMN "payment_date"
    `);
  }
}
