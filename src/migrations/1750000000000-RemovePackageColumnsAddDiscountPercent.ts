import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemovePackageColumnsAddDiscountPercent1750000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add discount_percent column to package_durations table
    await queryRunner.query(`
      ALTER TABLE "package_durations" 
      ADD COLUMN "discount_percent" DECIMAL(5,2) DEFAULT 0
    `);

    // Remove price and duration_days columns from packages table
    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN "price",
      DROP COLUMN "duration_days"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back price and duration_days columns to packages table
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "price" DECIMAL(10,2) NOT NULL DEFAULT 0,
      ADD COLUMN "duration_days" INTEGER NOT NULL DEFAULT 30
    `);

    // Copy data from first duration of each package to the package table
    await queryRunner.query(`
      UPDATE packages p
      SET 
        price = (SELECT price FROM package_durations pd WHERE pd.package_id = p.id ORDER BY pd.id LIMIT 1),
        duration_days = (SELECT duration_days FROM package_durations pd WHERE pd.package_id = p.id ORDER BY pd.id LIMIT 1)
    `);

    // Remove discount_percent column from package_durations table
    await queryRunner.query(`
      ALTER TABLE "package_durations" 
      DROP COLUMN "discount_percent"
    `);
  }
}
