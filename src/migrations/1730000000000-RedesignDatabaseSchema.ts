import { MigrationInterface, QueryRunner } from 'typeorm';

export class RedesignDatabaseSchema1730000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create package_users table
    await queryRunner.query(`
      CREATE TABLE "package_users" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL,
        "package_id" INTEGER NOT NULL,
        "start_date" TIMESTAMP NOT NULL DEFAULT now(),
        "expires_at" TIMESTAMP NOT NULL,
        "status" VARCHAR(50) NOT NULL DEFAULT 'active',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE,
        FOREIGN KEY ("package_id") REFERENCES "packages"("id") ON DELETE CASCADE
      )
    `);

    // Add index for faster lookups
    await queryRunner.query(`
      CREATE INDEX "idx_package_users_user_id" ON "package_users"("user_id");
      CREATE INDEX "idx_package_users_package_id" ON "package_users"("package_id");
    `);

    // Add cookie_data to accounts table (but not user_id)
    await queryRunner.query(`
      ALTER TABLE "accounts"
      ADD COLUMN "cookie_data" TEXT
    `);

    // Migrate data from cookies table to accounts table
    await queryRunner.query(`
      UPDATE "accounts" a
      SET "cookie_data" = (
        SELECT c."encrypted_cookie"
        FROM "cookies" c
        WHERE c."website_url" = a."website_url"
        LIMIT 1
      )
      WHERE EXISTS (
        SELECT 1
        FROM "cookies" c
        WHERE c."website_url" = a."website_url"
      )
    `);

    // Migrate data from existing account_packages to package_users
    // First, get all distinct user_id from sessions for each account
    await queryRunner.query(`
      INSERT INTO "package_users" ("user_id", "package_id", "expires_at", "status")
      SELECT DISTINCT s."user_id", ap."package_id", ap."expires_at",
        CASE
          WHEN ap."expires_at" < NOW() THEN 'expired'
          ELSE 'active'
        END as "status"
      FROM "account_packages" ap
      JOIN "sessions" s ON s."account_id" = ap."account_id"
      WHERE s."user_id" IS NOT NULL
    `);

    // Preserve account_packages relationships
    await queryRunner.query(`
      -- Ensure we don't lose any account-package relationships
      INSERT INTO "account_packages" ("account_id", "package_id", "assigned_at", "expires_at")
      SELECT ap."account_id", ap."package_id", ap."assigned_at", ap."expires_at"
      FROM "account_packages" ap
      WHERE NOT EXISTS (
        SELECT 1 FROM "account_packages" new_ap
        WHERE new_ap."account_id" = ap."account_id" AND new_ap."package_id" = ap."package_id"
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove columns from accounts table
    await queryRunner.query(`
      ALTER TABLE "accounts"
      DROP COLUMN "cookie_data"
    `);

    // Drop indexes
    await queryRunner.query(`
      DROP INDEX IF EXISTS "idx_package_users_user_id";
      DROP INDEX IF EXISTS "idx_package_users_package_id";
    `);

    // Drop account_packages table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "account_packages" CASCADE
    `);

    // Drop package_users table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "package_users" CASCADE
    `);
  }
}
