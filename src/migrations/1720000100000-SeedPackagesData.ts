import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedPackagesData1720000100000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Insert sample packages
    await queryRunner.query(`
      INSERT INTO packages (name, description, price, duration_days, created_at, updated_at)
      VALUES 
        ('Basic Package', 'Basic access to account features', 9.99, 30, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Premium Package', 'Premium access with additional features', 19.99, 60, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Enterprise Package', 'Full access to all features', 49.99, 90, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM packages;`);
  }
}
