import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPackageColumns1800000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add is_best_choice column
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "is_best_choice" BOOLEAN NOT NULL DEFAULT false
    `);

    // Add has_trail column
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "has_trail" BOOLEAN NOT NULL DEFAULT false
    `);

    // Add sort column
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "sort" INTEGER NOT NULL DEFAULT 0
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove columns in reverse order
    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN IF EXISTS "sort"
    `);

    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN IF EXISTS "has_trail"
    `);

    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN IF EXISTS "is_best_choice"
    `);
  }
}
