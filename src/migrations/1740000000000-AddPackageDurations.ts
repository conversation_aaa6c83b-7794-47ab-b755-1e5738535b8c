import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPackageDurations1740000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create package_durations table
    await queryRunner.query(`
      CREATE TABLE "package_durations" (
        "id" SERIAL PRIMARY KEY,
        "package_id" INTEGER NOT NULL,
        "duration_days" INTEGER NOT NULL,
        "price" DECIMAL(10,2) NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        FOREIGN KEY ("package_id") REFERENCES "packages"("id") ON DELETE CASCADE
      )
    `);

    // Migrate existing package data to package_durations
    await queryRunner.query(`
      INSERT INTO package_durations (package_id, duration_days, price, created_at, updated_at)
      SELECT id, duration_days, price, created_at, updated_at FROM packages
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "package_durations"`);
  }
}
