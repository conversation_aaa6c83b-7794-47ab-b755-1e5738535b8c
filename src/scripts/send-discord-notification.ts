import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DiscordNotificationService } from '../modules/discord/discord-notification.service';

/**
 * <PERSON><PERSON>t to manually send a Discord payment success notification
 * Run with: npx ts-node -r tsconfig-paths/register src/scripts/send-discord-notification.ts
 */
async function sendDiscordNotification() {
  try {
    // Create a standalone NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);

    // Get the Discord notification service
    const discordNotificationService = app.get(DiscordNotificationService);

    console.log('Sending Discord payment success notification...');

    // Send the notification
    const result =
      await discordNotificationService.sendPaymentSuccessNotification();

    if (result) {
      console.log('Discord notification sent successfully!');
    } else {
      console.error('Failed to send Discord notification');
    }

    // Close the application
    await app.close();
  } catch (error) {
    console.error('Error sending Discord notification:', error);
    process.exit(1);
  }
}

// Run the function
sendDiscordNotification()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
