<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>View Package</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
    <style>
      .modal {
        transition: opacity 0.25s ease;
      }
      body.modal-active {
        overflow-x: hidden;
        overflow-y: visible !important;
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Package Details</h1>
        <div>
          <button id="editBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">
            Edit Package
          </button>
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Packages
          </button>
        </div>
      </div>

      <!-- Package Details -->
      <div class="bg-white shadow rounded-lg p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 class="text-xl font-semibold mb-4">Package Information</h2>
            <div class="space-y-3">
              <div>
                <span class="text-gray-600 font-medium">ID:</span>
                <span id="package-id" class="ml-2"></span>
              </div>
              <div>
                <span class="text-gray-600 font-medium">Name:</span>
                <span id="package-name" class="ml-2"></span>
              </div>
              <div>
                <span class="text-gray-600 font-medium">Description:</span>
                <p id="package-description" class="mt-1 text-gray-700"></p>
              </div>
              <div>
                <span class="text-gray-600 font-medium">Best Choice:</span>
                <span id="package-best-choice" class="ml-2"></span>
              </div>
              <div>
                <span class="text-gray-600 font-medium">Has Trial:</span>
                <span id="package-has-trail" class="ml-2"></span>
              </div>
              <div>
                <span class="text-gray-600 font-medium">Sort Order:</span>
                <span id="package-sort" class="ml-2"></span>
              </div>
            </div>
          </div>
          <div>
            <h2 class="text-xl font-semibold mb-4">Package Details</h2>
            <div class="space-y-3">
              <div>
                <span class="text-gray-600 font-medium">Created:</span>
                <span id="package-created" class="ml-2"></span>
              </div>
              <div>
                <span class="text-gray-600 font-medium">Last Updated:</span>
                <span id="package-updated" class="ml-2"></span>
              </div>
            </div>

            <h2 class="text-xl font-semibold mb-4 mt-6">Features</h2>
            <div id="package-features" class="space-y-2 mb-6">
              <div class="text-gray-500 italic">Loading features...</div>
            </div>

            <h2 class="text-xl font-semibold mb-4 mt-6">Available Durations</h2>
            <div id="package-durations" class="space-y-3">
              <div class="text-gray-500 italic">Loading durations...</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Assigned Accounts -->
      <div class="bg-white shadow rounded-lg p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Assigned Accounts</h2>
          <div class="flex space-x-2">
            <button id="selectAllBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              Assign All Account
            </button>
            <button id="assignAccountBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
              Assign to Account
            </button>
          </div>
        </div>

        <div id="noAccountsMessage" class="py-4 text-center text-gray-500">
          No accounts assigned to this package yet.
        </div>

        <div id="accountsList" class="hidden space-y-4">
          <!-- Account items will be inserted here -->
        </div>
      </div>
    </div>

    <!-- Assign Account Modal -->
    <div id="assignModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Assign to Account</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideAssignModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <form id="assignAccountForm" class="space-y-4">
            <div>
              <label for="accountSelect" class="block text-sm font-medium text-gray-700">Select Account</label>
              <select
                id="accountSelect"
                name="accountSelect"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">-- Select an account --</option>
                <!-- Account options will be inserted here -->
              </select>
            </div>

            <div class="flex justify-end pt-2">
              <button type="button" class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideAssignModal()">Cancel</button>
              <button type="submit" class="px-4 bg-green-500 p-3 rounded-lg text-white hover:bg-green-600">Assign</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Remove Account Modal -->
    <div id="removeAccountModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Removal</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideRemoveAccountModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <p class="mb-6">Are you sure you want to remove this account from the package? This action cannot be undone.</p>
          <input type="hidden" id="removeAccountId" value="" />

          <div class="flex justify-end pt-2">
            <button type="button" class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideRemoveAccountModal()">Cancel</button>
            <button type="button" class="px-4 bg-red-500 p-3 rounded-lg text-white hover:bg-red-600" onclick="confirmRemoveAccount()">Remove</button>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Global variables
      const packageId = `<%= id %>`;

      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        const backgroundColor = type === 'success' ? '#48bb78' : '#f56565';

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Handle API errors
      function handleApiError(error) {
        console.error('API error:', error);

        if (error.response && error.response.status === 401) {
          window.location.href = '/login';
        } else {
          const message = error.response?.data?.message || 'An error occurred';
          showToast(message, 'error');
        }
      }

      // Fetch package data
      async function fetchPackage(id) {
        if (!setupAxios()) return null;

        try {
          const response = await axios.get(`/packages/${id}`);
          return response.data;
        } catch (error) {
          console.error('Error fetching package:', error);
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else if (error.response && error.response.status === 404) {
            showToast('Package not found', 'error');
            setTimeout(() => {
              window.location.href = '/admin/packages';
            }, 2000);
          } else {
            handleApiError(error);
          }
          return null;
        }
      }

      // Fetch accounts
      async function fetchAccounts() {
        if (!setupAxios()) return [];

        try {
          const response = await axios.get('/accounts?limit=100');
          return response.data.data;
        } catch (error) {
          handleApiError(error);
          return [];
        }
      }

      // Fetch accounts that are not assigned to the current package
      async function fetchUnassignedAccounts() {
        if (!setupAxios()) return [];

        try {
          // First get all accounts
          const allAccounts = await fetchAccounts();

          // Then get the current package with its assigned accounts
          const package_ = await fetchPackage(packageId);

          if (!package_ || !package_.accounts) {
            return allAccounts;
          }

          // Filter out accounts that are already assigned to this package
          const assignedAccountIds = package_.accounts.map(account => account.id);
          return allAccounts.filter(account => !assignedAccountIds.includes(account.id));
        } catch (error) {
          handleApiError(error);
          return [];
        }
      }

      // Display package features
      function displayPackageFeatures(features) {
        const featuresContainer = document.getElementById('package-features');

        if (!features || features.length === 0) {
          featuresContainer.innerHTML = '<div class="text-gray-500 italic">No features available</div>';
          return;
        }

        featuresContainer.innerHTML = '';

        features.forEach(feature => {
          const featureElement = document.createElement('div');
          featureElement.className = 'bg-gray-50 p-3 rounded-lg border mb-3';

          // Handle both old string format and new object format
          if (typeof feature === 'string') {
            featureElement.innerHTML = `
              <div class="flex items-center">
                <svg class="h-4 w-4 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>${feature}</span>
              </div>
            `;
          } else {
            // New object format with image
            const hasImage = feature.img && feature.img.trim() !== '';

            featureElement.innerHTML = `
              <div class="flex flex-col">
                <div class="flex items-center mb-2">
                  <svg class="h-4 w-4 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="font-medium">${feature.description}</span>
                </div>
                ${hasImage ? `
                <div class="mt-2">
                  <img src="${feature.img}" class="h-24 object-contain rounded" alt="${feature.description}">
                </div>
                ` : ''}
              </div>
            `;
          }

          featuresContainer.appendChild(featureElement);
        });
      }

      // Display package details
      function displayPackageDetails(package_) {
        document.getElementById('package-id').textContent = package_.id;
        document.getElementById('package-name').textContent = package_.name;
        document.getElementById('package-description').textContent = package_.description || 'No description provided';

        // Display new fields
        document.getElementById('package-best-choice').textContent = package_.is_best_choice ? 'Yes' : 'No';
        document.getElementById('package-has-trail').textContent = package_.has_trail ? 'Yes' : 'No';
        document.getElementById('package-sort').textContent = package_.sort || '0';

        // Format dates
        const createdDate = new Date(package_.created_at).toLocaleString();
        const updatedDate = new Date(package_.updated_at).toLocaleString();

        document.getElementById('package-created').textContent = createdDate;
        document.getElementById('package-updated').textContent = updatedDate;

        // Display package features
        displayPackageFeatures(package_.features || []);

        // Display package durations
        displayPackageDurations(package_.durations || []);

        // Display assigned accounts
        displayAssignedAccounts(package_.accounts || []);
      }

      // Display package durations
      function displayPackageDurations(durations) {
        const durationsContainer = document.getElementById('package-durations');

        if (!durations || durations.length === 0) {
          durationsContainer.innerHTML = '<div class="text-gray-500 italic">No durations available</div>';
          return;
        }

        durationsContainer.innerHTML = '';

        // Sort durations by duration_days
        durations.sort((a, b) => a.duration_days - b.duration_days);

        durations.forEach(duration => {
          const durationElement = document.createElement('div');
          durationElement.className = 'bg-gray-50 p-3 rounded-lg border flex justify-between items-center';

          // Calculate discounted price if discount is present
          const hasDiscount = duration.discount_percent && duration.discount_percent > 0;
          const discountedPrice = hasDiscount
            ? (duration.price * (1 - duration.discount_percent / 100)).toFixed(2)
            : null;

          const priceDisplay = hasDiscount
            ? `<span class="line-through text-gray-500">$${duration.price}</span> <span class="text-green-600 font-medium">$${discountedPrice}</span>`
            : `<span class="text-green-600 font-medium">$${duration.price}</span>`;

          const discountDisplay = hasDiscount
            ? `<span class="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded">-${duration.discount_percent}%</span>`
            : '';

          durationElement.innerHTML = `
            <div>
              <span class="font-medium">${duration.duration_days} days</span>
              <span class="ml-4">${priceDisplay}${discountDisplay}</span>
            </div>
            <div class="text-xs text-gray-500">
              ID: ${duration.id}
            </div>
          `;

          durationsContainer.appendChild(durationElement);
        });
      }

      // Display assigned accounts
      function displayAssignedAccounts(accounts) {
        const noAccountsElement = document.getElementById('noAccountsMessage');
        const accountsListElement = document.getElementById('accountsList');

        if (!accounts || accounts.length === 0) {
          noAccountsElement.classList.remove('hidden');
          accountsListElement.classList.add('hidden');
          return;
        }

        noAccountsElement.classList.add('hidden');
        accountsListElement.classList.remove('hidden');
        accountsListElement.innerHTML = '';

        accounts.forEach(account => {
          const accountElement = document.createElement('div');
          accountElement.className = 'bg-gray-50 p-4 rounded-lg border';

          accountElement.innerHTML = `
            <div class="flex justify-between items-center">
              <div>
                <h3 class="font-semibold">${account.name || account.username || 'Unnamed Account'}</h3>
                <p class="text-sm text-gray-600">${account.website_url}</p>
              </div>
              <button
                onclick="removeAccountAssignment(${account.id})"
                class="text-red-600 hover:text-red-800 font-medium"
              >
                Remove
              </button>
            </div>
          `;

          accountsListElement.appendChild(accountElement);
        });
      }

      // Show assign account modal
      function showAssignModal() {
        document.getElementById('assignModal').classList.remove('hidden');
        document.getElementById('assignModal').classList.remove('opacity-0');
        document.getElementById('assignModal').classList.add('opacity-100');
        document.getElementById('assignModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      // Hide assign account modal
      function hideAssignModal() {
        document.getElementById('assignModal').classList.add('opacity-0');
        document.getElementById('assignModal').classList.remove('opacity-100');
        document.getElementById('assignModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('assignModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
      }

      // Populate account select dropdown
      async function populateAccountSelect() {
        // Get only unassigned accounts
        const unassignedAccounts = await fetchUnassignedAccounts();
        const selectElement = document.getElementById('accountSelect');

        // Clear existing options except the first one
        while (selectElement.options.length > 1) {
          selectElement.remove(1);
        }

        if (unassignedAccounts.length === 0) {
          // Add a disabled option indicating no accounts available
          const option = document.createElement('option');
          option.disabled = true;
          option.textContent = 'No unassigned accounts available';
          selectElement.appendChild(option);
        } else {
          unassignedAccounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = `${account.name || account.username || 'Unnamed'} - ${account.website_url}`;
            selectElement.appendChild(option);
          });
        }
      }

      // Assign package to account
      async function assignToAccount(accountId) {
        if (!setupAxios()) return;

        try {
          // Check if account is already assigned to this package
          const package_ = await fetchPackage(packageId);
          if (package_ && package_.accounts) {
            const isAlreadyAssigned = package_.accounts.some(account => account.id === parseInt(accountId));
            if (isAlreadyAssigned) {
              showToast('This account is already assigned to this package', 'error');
              return false;
            }
          }

          await axios.post(`/packages/${packageId}/accounts/${accountId}`);
          showToast('Package assigned to account successfully');
          hideAssignModal();

          // Refresh package data
          const updatedPackage = await fetchPackage(packageId);
          if (updatedPackage) {
            displayPackageDetails(updatedPackage);
          }
          return true;
        } catch (error) {
          handleApiError(error);
          return false;
        }
      }

      // Assign package to all unassigned accounts
      async function assignToAllAccounts() {
        if (!setupAxios()) return;

        try {
          // Get all unassigned accounts
          const unassignedAccounts = await fetchUnassignedAccounts();

          if (unassignedAccounts.length === 0) {
            showToast('No unassigned accounts found', 'error');
            return;
          }

          let successCount = 0;
          let errorCount = 0;

          // Show a loading toast
          showToast(`Assigning ${unassignedAccounts.length} accounts to package...`, 'success');

          // Assign each account one by one
          for (const account of unassignedAccounts) {
            try {
              const success = await assignToAccount(account.id);
              if (success) {
                successCount++;
              } else {
                errorCount++;
              }
            } catch (error) {
              errorCount++;
              console.error(`Error assigning account ${account.id}:`, error);
            }
          }

          // Show final result
          if (successCount > 0) {
            showToast(`Successfully assigned ${successCount} accounts to package`, 'success');
          }

          if (errorCount > 0) {
            showToast(`Failed to assign ${errorCount} accounts`, 'error');
          }

          // Refresh package data
          const package_ = await fetchPackage(packageId);
          if (package_) {
            displayPackageDetails(package_);
          }
        } catch (error) {
          handleApiError(error);
        }
      }

      // Show remove account modal
      function showRemoveAccountModal(accountId) {
        document.getElementById('removeAccountId').value = accountId;
        document.getElementById('removeAccountModal').classList.remove('hidden');
        document.getElementById('removeAccountModal').classList.remove('opacity-0');
        document.getElementById('removeAccountModal').classList.add('opacity-100');
        document.getElementById('removeAccountModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      // Hide remove account modal
      function hideRemoveAccountModal() {
        document.getElementById('removeAccountModal').classList.add('opacity-0');
        document.getElementById('removeAccountModal').classList.remove('opacity-100');
        document.getElementById('removeAccountModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('removeAccountModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
      }

      // Remove package from account (show confirmation modal)
      function removeAccountAssignment(accountId) {
        if (!setupAxios()) return;
        showRemoveAccountModal(accountId);
      }

      // Confirm remove account from package
      async function confirmRemoveAccount() {
        if (!setupAxios()) return;

        const accountId = document.getElementById('removeAccountId').value;
        if (!accountId) return;

        try {
          await axios.delete(`/packages/${packageId}/accounts/${accountId}`);
          showToast('Account removed from package successfully');
          hideRemoveAccountModal();

          // Refresh package data
          const package_ = await fetchPackage(packageId);
          if (package_) {
            displayPackageDetails(package_);
          }
        } catch (error) {
          handleApiError(error);
        }
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        setupAxios();
        if (!(await checkAuth())) return;

        // Fetch and display package data
        const package_ = await fetchPackage(packageId);
        if (package_) {
          displayPackageDetails(package_);
        }

        // Edit button
        document.getElementById('editBtn').addEventListener('click', () => {
          window.location.href = `/admin/packages/${packageId}/edit`;
        });

        // Back button
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/packages';
        });

        // Assign account button
        document.getElementById('assignAccountBtn').addEventListener('click', async () => {
          await populateAccountSelect();
          showAssignModal();
        });

        // Select All button
        document.getElementById('selectAllBtn').addEventListener('click', async () => {
          // Show confirmation dialog
          if (confirm('Are you sure you want to assign all unassigned accounts to this package?')) {
            await assignToAllAccounts();
          }
        });

        // Assign account form submission
        document.getElementById('assignAccountForm').addEventListener('submit', async (e) => {
          e.preventDefault();

          const accountId = document.getElementById('accountSelect').value;

          if (!accountId) {
            showToast('Please select an account', 'error');
            return;
          }

          await assignToAccount(accountId);
        });
      });
    </script>
  </body>
</html>
