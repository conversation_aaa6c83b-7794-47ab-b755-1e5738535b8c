<%- include('../../partials/layout', {
  title: 'Cookie Management',
  currentPath: '/admin/cookies',
  body: `
    <div class="container mx-auto">
      <div class="flex justify-between items-center mb-6">
        <div>
          <button id="createCookieBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Add New Cookie
          </button>
          <button id="backToHomeBtn" class="ml-2 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Home
          </button>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="bg-white shadow rounded-lg p-4 mb-6">
        <div class="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
          <div class="flex-1">
            <label for="filterAccountSelect" class="block text-sm font-medium text-gray-700 mb-1">Filter by Account</label>
            <select
              id="filterAccountSelect"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">All Accounts</option>
              <!-- Account options will be inserted here -->
            </select>
          </div>
          <div class="flex-1">
            <label for="filterServerSelect" class="block text-sm font-medium text-gray-700 mb-1">Filter by Server</label>
            <select
              id="filterServerSelect"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">All Servers</option>
              <option value="1">Server 1</option>
              <option value="2">Server 2</option>
              <option value="3">Server 3</option>
            </select>
          </div>
          <div class="w-32">
            <label class="block text-sm font-medium text-gray-700 mb-1">&nbsp;</label>
            <button id="applyFilterBtn" class="w-full bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
              Apply Filter
            </button>
          </div>
        </div>
      </div>

      <!-- Cookies Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Website URL</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Server</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="cookiesTableBody" class="bg-white divide-y divide-gray-200">
            <!-- Cookie rows will be inserted here -->
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex justify-between items-center mt-4 bg-white shadow rounded-lg p-4">
        <div>
          <button id="prevPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed">
            Previous
          </button>
          <button id="nextPageBtn" class="ml-2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed">
            Next
          </button>
        </div>
        <div class="text-gray-600">
          Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
        </div>
      </div>
    </div>

    <!-- Add/Edit Cookie Modal -->
    <div id="cookieModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center hidden z-50">
      <div class="modal-overlay absolute inset-0 bg-black opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-2xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold" id="modalTitle">Add New Cookie</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideCookieModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <form id="cookieForm" class="space-y-4">
            <input type="hidden" id="cookieId" value="" />
            <input type="hidden" id="accountId" value="" />

            <div>
              <label for="accountSelect" class="block text-sm font-medium text-gray-700 mb-1">Account</label>
              <select
                id="accountSelect"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                required
              >
                <option value="">Select an account</option>
                <!-- Account options will be inserted here -->
              </select>
            </div>

            <div>
              <label for="serverSelect" class="block text-sm font-medium text-gray-700 mb-1">Server</label>
              <select
                id="serverSelect"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                required
              >
                <option value="1">Server 1</option>
                <option value="2">Server 2</option>
                <option value="3">Server 3</option>
              </select>
            </div>

            <div>
              <label for="cookieData" class="block text-sm font-medium text-gray-700 mb-1">Cookie Data</label>
              <textarea
                id="cookieData"
                rows="8"
                class="cookie-data-textarea w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Paste cookie data here..."
                required
              ></textarea>
              <p class="text-xs text-gray-500 mt-1">
                Format: name value domain path expires
              </p>
              <p class="text-xs text-gray-500">
                Example: cf_bm ofIRXXFKDVBpYJJSSvP6hNZSAJ9Uw2HZElJenQoxuFk-1746500105-1.0.1.1-iOD0SjiUYBjn5RBvA02zuhfVkyD12Q.03thb1EEQcGZHEVPkUq3YbZeqe_8iscdhy0qPFnmaLlAbEDdsL1P3a.q1RQT0gmkebODorisK1is .midjourney.com / 2025-05-06T03:25:04.887Z
              </p>
              <p class="text-xs text-gray-500">
                The system will automatically convert this to the required JSON format.
              </p>
            </div>

            <div class="flex justify-end pt-2">
              <button type="button" class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideCookieModal()">
                Cancel
              </button>
              <button type="submit" class="px-4 bg-blue-500 p-3 rounded-lg text-white hover:bg-blue-600">
                Save Cookie
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center hidden z-50">
      <div class="modal-overlay absolute inset-0 bg-black opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Delete</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideDeleteModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <p>Are you sure you want to delete this cookie? This action cannot be undone.</p>

          <div class="flex justify-end pt-2">
            <button class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideDeleteModal()">Cancel</button>
            <button class="px-4 bg-red-500 p-3 rounded-lg text-white hover:bg-red-600" onclick="deleteCookie()">Delete</button>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalHead: `
    <style>
      .cookie-data-textarea {
        font-family: monospace;
        white-space: pre;
      }
    </style>
  `,
  additionalScripts: `
    <script>
      // Global variables
      let currentPage = 1;
      let filterAccountId = '';
      let filterServerId = '';
      let deleteAccountId = null;
      let deleteServerId = null;

      // Load cookies with pagination and filters
      async function loadCookies(page = 1) {
        try {
          let url = '/accounts/cookies?page=' + page + '&limit=10';

          // Add account filter if selected
          if (filterAccountId) {
            url += '&accountId=' + encodeURIComponent(filterAccountId);
          }

          // Add server filter if selected
          if (filterServerId) {
            url += '&serverId=' + encodeURIComponent(filterServerId);
          }

          const response = await axios.get(url);
          const cookies = response.data.data;
          const totalPages = response.data.meta.totalPages || 1;

          document.getElementById('currentPage').textContent = page;
          document.getElementById('totalPages').textContent = totalPages;

          // Update pagination buttons
          document.getElementById('prevPageBtn').disabled = page <= 1;
          document.getElementById('nextPageBtn').disabled = page >= totalPages;

          renderCookiesTable(cookies);
        } catch (error) {
          handleApiError(error);
        }
      }

      // Load accounts for dropdowns
      async function loadAccounts() {
        try {
          const response = await axios.get('/accounts');
          const accounts = response.data.data;

          // Populate the form dropdown
          const accountSelect = document.getElementById('accountSelect');
          accountSelect.innerHTML = '<option value="">Select an account</option>';

          // Populate the filter dropdown
          const filterAccountSelect = document.getElementById('filterAccountSelect');
          filterAccountSelect.innerHTML = '<option value="">All Accounts</option>';

          accounts.forEach(account => {
            // For the form dropdown
            const option1 = document.createElement('option');
            option1.value = account.id;
            option1.textContent = account.name || account.website_url + ' (' + account.id + ')';
            accountSelect.appendChild(option1);

            // For the filter dropdown
            const option2 = document.createElement('option');
            option2.value = account.id;
            option2.textContent = account.name || account.website_url + ' (' + account.id + ')';
            filterAccountSelect.appendChild(option2);
          });
        } catch (error) {
          handleApiError(error);
        }
      }

      // Render cookies table
      function renderCookiesTable(cookies) {
        const tableBody = document.getElementById('cookiesTableBody');
        tableBody.innerHTML = '';

        if (cookies.length === 0) {
          const emptyRow = document.createElement('tr');
          emptyRow.innerHTML = 
            '<td colspan="6" class="px-6 py-4 text-center text-gray-500">' +
              'No cookies found' +
            '</td>';
          tableBody.appendChild(emptyRow);
          return;
        }

        cookies.forEach(cookie => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-gray-50';

          const lastUpdated = new Date(cookie.updatedAt || cookie.lastUsed || cookie.createdAt).toLocaleString();

          row.innerHTML = 
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' + cookie.id + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (cookie.account?.name || 'N/A') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (cookie.account?.website_url || 'N/A') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Server ' + cookie.serverId + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + lastUpdated + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">' +
              '<div class="flex space-x-2">' +
                '<button onclick="editCookie(' + cookie.account?.id + ', ' + cookie.serverId + ')" class="text-blue-600 hover:text-blue-900">Edit</button>' +
                '<button onclick="showDeleteModal(' + cookie.account?.id + ', ' + cookie.serverId + ')" class="text-red-600 hover:text-red-900">Delete</button>' +
              '</div>' +
            '</td>';

          tableBody.appendChild(row);
        });
      }

      // Show cookie modal for add or edit
      function showCookieModal(mode, accountId = null, serverId = 1) {
        const modal = document.getElementById('cookieModal');
        const modalTitle = document.getElementById('modalTitle');
        const cookieForm = document.getElementById('cookieForm');
        const accountIdInput = document.getElementById('accountId');
        const accountSelect = document.getElementById('accountSelect');
        const serverSelect = document.getElementById('serverSelect');
        const cookieData = document.getElementById('cookieData');

        // Reset form
        cookieForm.reset();

        if (mode === 'edit' && accountId) {
          modalTitle.textContent = 'Edit Cookie';
          accountIdInput.value = accountId;
          accountSelect.value = accountId;
          accountSelect.disabled = true;
          serverSelect.value = serverId;

          // Load existing cookie data
          loadCookieData(accountId, serverId);
        } else {
          modalTitle.textContent = 'Add New Cookie';
          accountIdInput.value = '';
          accountSelect.disabled = false;
          cookieData.value = '';
        }

        modal.classList.remove('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.add('modal-active');
      }

      // Hide cookie modal
      function hideCookieModal() {
        const modal = document.getElementById('cookieModal');
        modal.classList.add('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.remove('modal-active');
      }

      // Show delete confirmation modal
      function showDeleteModal(accountId, serverId) {
        deleteAccountId = accountId;
        deleteServerId = serverId;
        const modal = document.getElementById('deleteModal');
        modal.classList.remove('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.add('modal-active');
      }

      // Hide delete modal
      function hideDeleteModal() {
        const modal = document.getElementById('deleteModal');
        modal.classList.add('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.remove('modal-active');
        deleteAccountId = null;
        deleteServerId = null;
      }

      // Load cookie data for editing
      async function loadCookieData(accountId, serverId) {
        try {
          const response = await axios.get('/accounts/' + accountId + '/cookies/' + serverId);
          document.getElementById('cookieData').value = response.data.cookieData || '';
        } catch (error) {
          handleApiError(error);
        }
      }

      // Save cookie
      async function saveCookie() {
        try {
          const accountId = document.getElementById('accountId').value || document.getElementById('accountSelect').value;
          const serverId = document.getElementById('serverSelect').value;
          const cookieData = document.getElementById('cookieData').value;

          if (!accountId || !cookieData) {
            showToast('Please fill in all required fields', 'error');
            return;
          }

          await axios.post('/accounts/' + accountId + '/cookie', {
            cookie_data: cookieData,
            server_id: serverId
          });

          showToast('Cookie saved successfully');
          hideCookieModal();
          loadCookies(currentPage);
        } catch (error) {
          handleApiError(error);
        }
      }

      // Delete cookie
      async function deleteCookie() {
        if (!deleteAccountId || !deleteServerId) return;

        try {
          await axios.delete('/accounts/' + deleteAccountId + '/cookies/' + deleteServerId);
          showToast('Cookie deleted successfully');
          hideDeleteModal();
          loadCookies(currentPage);
        } catch (error) {
          hideDeleteModal();
          handleApiError(error);
        }
      }

      // Edit cookie
      function editCookie(accountId, serverId) {
        showCookieModal('edit', accountId, serverId);
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        await loadAccounts();
        loadCookies(currentPage);

        // Event listeners
        document.getElementById('createCookieBtn').addEventListener('click', () => {
          showCookieModal('add');
        });

        document.getElementById('backToHomeBtn').addEventListener('click', () => {
          window.location.href = '/';
        });

        document.getElementById('prevPageBtn').addEventListener('click', () => {
          if (currentPage > 1) {
            currentPage--;
            loadCookies(currentPage);
          }
        });

        document.getElementById('nextPageBtn').addEventListener('click', () => {
          const totalPages = parseInt(document.getElementById('totalPages').textContent);
          if (currentPage < totalPages) {
            currentPage++;
            loadCookies(currentPage);
          }
        });

        document.getElementById('applyFilterBtn').addEventListener('click', () => {
          filterAccountId = document.getElementById('filterAccountSelect').value;
          filterServerId = document.getElementById('filterServerSelect').value;
          currentPage = 1;
          loadCookies(currentPage);
        });

        document.getElementById('cookieForm').addEventListener('submit', (e) => {
          e.preventDefault();
          saveCookie();
        });

        // Modal close buttons
        document.querySelectorAll('.modal-close').forEach(button => {
          button.addEventListener('click', () => {
            hideCookieModal();
            hideDeleteModal();
          });
        });

        // Modal overlays
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
          overlay.addEventListener('click', () => {
            hideCookieModal();
            hideDeleteModal();
          });
        });
      });
    </script>
  `
}) %>
