<%- include('../../partials/layout', {
  title: 'Account Management',
  currentPath: '/admin/accounts',
  body: `
    <div class="container mx-auto">
      <div class="flex justify-between items-center mb-6">
        <div>
          <button id="createAccountBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Create New Account
          </button>
          <button id="backToHomeBtn" class="ml-2 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Home
          </button>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="bg-white shadow rounded-lg p-4 mb-6">
        <div class="flex flex-wrap gap-4">
          <div class="flex-1">
            <label for="searchInput" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input type="text" id="searchInput" placeholder="Search by website or name..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Filter</label>
            <select id="filterSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
              <option value="all">All Accounts</option>
              <option value="with_packages">With Packages</option>
              <option value="without_packages">Without Packages</option>
            </select>
          </div>
          <div class="flex items-end">
            <button id="applyFilterBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              Apply
            </button>
          </div>
        </div>
      </div>

      <!-- Accounts Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button onclick="sortBy('sort')" class="flex items-center hover:text-gray-700">
                  Sort
                  <svg class="ml-1 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5 8l5-5 5 5H5z"/>
                  </svg>
                </button>
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button onclick="sortBy('name')" class="flex items-center hover:text-gray-700">
                  Name
                  <svg class="ml-1 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5 8l5-5 5 5H5z"/>
                  </svg>
                </button>
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Packages</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="accountsTableBody" class="bg-white divide-y divide-gray-200">
            <!-- Account rows will be inserted here -->
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex justify-between items-center mt-6">
        <button id="prevPageBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
          Previous
        </button>
        <div class="text-gray-600">
          Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
        </div>
        <button id="nextPageBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
          Next
        </button>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center hidden z-50">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Delete</p>
            <div class="modal-close cursor-pointer z-50">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>
          <p class="mb-4">Are you sure you want to delete this account? This action cannot be undone.</p>
          <div class="flex justify-end pt-2">
            <button id="cancelDeleteBtn" class="px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2">Cancel</button>
            <button id="confirmDeleteBtn" class="px-4 bg-red-500 p-3 rounded-lg text-white hover:bg-red-600">Delete</button>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <script>
      // Global variables
      let currentPage = 1;
      let searchTerm = '';
      let filterValue = 'all';
      let deleteAccountId = null;
      let currentSortBy = '';
      let currentSortOrder = 'ASC';

      // Load accounts with pagination, search and filter
      async function loadAccounts(page = 1) {
        try {
          let url = '/accounts?page=' + page + '&limit=50';

          // Add search and filter parameters if they exist
          if (searchTerm) {
            url += '&search=' + encodeURIComponent(searchTerm);
          }

          if (filterValue !== 'all') {
            url += '&filter=' + filterValue;
          }

          // Add sorting parameters if they exist
          if (currentSortBy) {
            url += '&sortBy=' + currentSortBy + '&sortOrder=' + currentSortOrder;
          }

          const response = await axios.get(url);
          const accounts = response.data.data;
          const totalPages = response.data.meta.totalPages;

          document.getElementById('currentPage').textContent = page;
          document.getElementById('totalPages').textContent = totalPages;

          renderAccountsTable(accounts);
        } catch (error) {
          handleApiError(error);
        }
      }

      // Render accounts table
      function renderAccountsTable(accounts) {
        const tableBody = document.getElementById('accountsTableBody');
        tableBody.innerHTML = '';

        if (accounts.length === 0) {
          const emptyRow = document.createElement('tr');
          emptyRow.innerHTML =
            '<td colspan="6" class="px-6 py-4 text-center text-gray-500">' +
              'No accounts found' +
            '</td>';
          tableBody.appendChild(emptyRow);
          return;
        }

        accounts.forEach(account => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-gray-50';

          // Format date
          const lastLogin = account.last_login
            ? new Date(account.last_login).toLocaleDateString()
            : 'Never';

          // Format packages
          const packagesCount = account.packages ? account.packages.length : 0;

          row.innerHTML =
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' + account.id + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (account.sort || 0) + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (account.name || '-') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + lastLogin + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + packagesCount + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">' +
              '<div class="flex space-x-2">' +
                '<button onclick="viewAccount(' + account.id + ')" class="text-indigo-600 hover:text-indigo-900">View</button>' +
                '<button onclick="editAccount(' + account.id + ')" class="text-blue-600 hover:text-blue-900">Edit</button>' +
                '<button onclick="showDeleteModal(' + account.id + ')" class="text-red-600 hover:text-red-900">Delete</button>' +
                '<button onclick="autoLogin(' + account.id + ')" class="text-green-600 hover:text-green-900">Login</button>' +
              '</div>' +
            '</td>';

          tableBody.appendChild(row);
        });
      }

      // View account details
      function viewAccount(id) {
        window.location.href = '/admin/accounts/' + id;
      }

      // Edit account
      function editAccount(id) {
        window.location.href = '/admin/accounts/' + id + '/edit';
      }

      // Show delete confirmation modal
      function showDeleteModal(id) {
        deleteAccountId = id;
        const modal = document.getElementById('deleteModal');
        modal.classList.remove('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.add('modal-active');
      }

      // Hide delete confirmation modal
      function hideDeleteModal() {
        const modal = document.getElementById('deleteModal');
        modal.classList.add('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.remove('modal-active');
        deleteAccountId = null;
      }

      // Delete account
      async function deleteAccount() {
        if (!deleteAccountId) return;

        try {
          await axios.delete('/accounts/' + deleteAccountId);
          hideDeleteModal();
          showToast('Account deleted successfully');
          loadAccounts(currentPage);
        } catch (error) {
          hideDeleteModal();
          handleApiError(error);
        }
      }

      // Auto login
      async function autoLogin(accountId) {
        try {
          const response = await axios.post('/accounts/' + accountId + '/login');
          showToast('Auto login initiated successfully');
        } catch (error) {
          handleApiError(error);
        }
      }

      // Sort by column
      function sortBy(column) {
        if (currentSortBy === column) {
          // Toggle sort order if same column
          currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
        } else {
          // Set new column and default to ASC
          currentSortBy = column;
          currentSortOrder = 'ASC';
        }
        currentPage = 1;
        loadAccounts(currentPage);
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial load
        loadAccounts(currentPage);

        // Event listeners
        document.getElementById('createAccountBtn').addEventListener('click', () => {
          window.location.href = '/admin/accounts/create';
        });

        document.getElementById('backToHomeBtn').addEventListener('click', () => {
          window.location.href = '/';
        });

        document.getElementById('prevPageBtn').addEventListener('click', () => {
          if (currentPage > 1) {
            currentPage--;
            loadAccounts(currentPage);
          }
        });

        document.getElementById('nextPageBtn').addEventListener('click', () => {
          const totalPages = parseInt(document.getElementById('totalPages').textContent);
          if (currentPage < totalPages) {
            currentPage++;
            loadAccounts(currentPage);
          }
        });

        document.getElementById('applyFilterBtn').addEventListener('click', () => {
          searchTerm = document.getElementById('searchInput').value.trim();
          filterValue = document.getElementById('filterSelect').value;
          currentPage = 1;
          loadAccounts(currentPage);
        });

        // Modal close buttons
        document.querySelectorAll('.modal-close').forEach(button => {
          button.addEventListener('click', hideDeleteModal);
        });

        // Modal overlays
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
          overlay.addEventListener('click', hideDeleteModal);
        });

        // Delete buttons
        document.getElementById('cancelDeleteBtn').addEventListener('click', hideDeleteModal);
        document.getElementById('confirmDeleteBtn').addEventListener('click', deleteAccount);
      });
    </script>
  `
}) %>
