<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin - Edit Account</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-2xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Edit Account</h1>
        <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
          Back to Accounts
        </button>
      </div>

      <div class="bg-white shadow rounded-lg p-6">
        <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>
        <div id="successMessage" class="hidden mb-4 p-4 text-green-700 bg-green-100 rounded-md"></div>

        <form id="editAccountForm" class="space-y-4">
          <!-- Account ID (hidden) -->
          <input type="hidden" id="accountId" name="accountId" />

          <!-- Website URL -->
          <div>
            <label for="website_url" class="block text-sm font-medium text-gray-700">Website URL *</label>
            <input
              type="url"
              id="website_url"
              name="website_url"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="https://example.com"
            />
          </div>

          <!-- Username -->
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
            <input
              type="text"
              id="username"
              name="username"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Username"
            />
          </div>

          <!-- Password -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Leave blank to keep current password"
            />
            <p class="mt-1 text-sm text-gray-500">Leave blank to keep the current password</p>
          </div>

          <!-- Login Cookie -->
          <div>
            <label for="login_cookie" class="block text-sm font-medium text-gray-700">Login Cookie</label>
            <select
              id="login_cookie"
              name="login_cookie"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="1">Yes (1)</option>
              <option value="0">No (0)</option>
            </select>
          </div>

          <!-- Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Display name"
            />
          </div>

          <!-- Description -->
          <div>
            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              id="description"
              name="description"
              rows="3"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Enter account description"
            ></textarea>
          </div>

          <!-- Sort Order -->
          <div>
            <label for="sort" class="block text-sm font-medium text-gray-700">Sort Order</label>
            <input
              type="number"
              id="sort"
              name="sort"
              min="0"
              step="1"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="0"
            />
            <p class="mt-1 text-sm text-gray-500">Lower numbers appear first (0 = default)</p>
          </div>

          <!-- Image Intro -->
          <div>
            <label for="img_intro" class="block text-sm font-medium text-gray-700">Image Intro</label>
            <div class="mt-1 flex items-center">
              <input
                type="file"
                id="img_intro_file"
                accept="image/*"
                class="block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-semibold
                  file:bg-indigo-50 file:text-indigo-700
                  hover:file:bg-indigo-100"
              />
              <!-- Hidden input to store Base64 data -->
              <input type="hidden" id="img_intro" name="img_intro" />
            </div>
            <div id="current_image" class="mt-2">
              <p class="text-sm text-gray-500">Current image:</p>
              <img id="current_image_preview" class="mt-1 max-h-40 rounded-md hidden" />
              <span id="current_image_name">None</span>
            </div>
            <div id="image_preview" class="mt-2 hidden">
              <p class="text-sm text-gray-500">New image preview:</p>
              <img id="preview_image" class="mt-1 max-h-40 rounded-md" />
            </div>
            <p class="mt-1 text-sm text-gray-500">Upload a new image to replace the current one (JPG, PNG, GIF)</p>
          </div>

          <!-- Last Login (Read-only) -->
          <div>
            <label for="last_login" class="block text-sm font-medium text-gray-700">Last Login</label>
            <input
              type="text"
              id="last_login"
              name="last_login"
              readonly
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50"
            />
          </div>

          <div class="pt-4">
            <button
              type="submit"
              id="submitButton"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Update Account
            </button>
          </div>
        </form>
      </div>
    </div>

    <script>
      // Get account ID from server-rendered data
      const accountId = '<%= id %>'; // Use the ID passed from the server

      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show error message
      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        document.getElementById('successMessage').classList.add('hidden');
        document.getElementById('submitButton').disabled = false;
        document.getElementById('submitButton').textContent = 'Update Account';
      }

      // Show success message
      function showSuccess(message) {
        const successMessage = document.getElementById('successMessage');
        successMessage.textContent = message;
        successMessage.classList.remove('hidden');
        document.getElementById('errorMessage').classList.add('hidden');
        document.getElementById('submitButton').disabled = false;
        document.getElementById('submitButton').textContent = 'Update Account';
      }

      // Fetch account details
      async function fetchAccount(id) {
        if (!setupAxios()) return;

        try {
          const response = await axios.get(`/accounts/${id}`);
          return response.data;
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to fetch account details. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Populate form with account details
      function populateForm(account) {
        document.getElementById('accountId').value = account.id;
        document.getElementById('website_url').value = account.website_url || '';
        document.getElementById('username').value = account.username || '';
        document.getElementById('login_cookie').value = account.login_cookie.toString();
        document.getElementById('name').value = account.name || '';
        document.getElementById('description').value = account.description || '';
        document.getElementById('sort').value = account.sort || 0;

        // Handle image display
        if (account.img_intro) {
          // Check if it's a Base64 image or a URL
          if (account.img_intro.startsWith('data:image')) {
            // It's a Base64 image
            document.getElementById('current_image_preview').src = account.img_intro;
            document.getElementById('current_image_preview').classList.remove('hidden');
            document.getElementById('current_image_name').classList.add('hidden');
          } else {
            // It's a URL or filename
            document.getElementById('current_image_name').textContent = account.img_intro.split('/').pop() || account.img_intro;
            document.getElementById('current_image_preview').classList.add('hidden');
            document.getElementById('current_image_name').classList.remove('hidden');
          }
          document.getElementById('current_image').classList.remove('hidden');
        } else {
          document.getElementById('current_image_name').textContent = 'None';
          document.getElementById('current_image_preview').classList.add('hidden');
          document.getElementById('current_image_name').classList.remove('hidden');
        }

        // Format last login date
        const lastLogin = account.last_login
          ? new Date(account.last_login).toLocaleString()
          : 'Never';
        document.getElementById('last_login').value = lastLogin;
      }

      // Update account
      async function updateAccount(id, formData) {
        if (!setupAxios()) return;

        try {
          console.log('Sending data to server:', formData);

          const response = await axios.patch(`/accounts/${id}`, formData);
          showSuccess('Account updated successfully!');
          return response.data;
        } catch (error) {
          console.error('Error updating account:', error);
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to update account. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Convert file to Base64
      function convertToBase64(file) {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => resolve(reader.result);
          reader.onerror = error => reject(error);
        });
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        setupAxios();

        // Image file input change handler
        document.getElementById('img_intro_file').addEventListener('change', async function(e) {
          const file = e.target.files[0];
          if (file) {
            try {
              const base64String = await convertToBase64(file);
              document.getElementById('img_intro').value = base64String;

              // Show preview
              const previewImage = document.getElementById('preview_image');
              previewImage.src = base64String;
              document.getElementById('image_preview').classList.remove('hidden');
            } catch (error) {
              console.error('Error converting image to Base64:', error);
              showError('Failed to process the image. Please try again.');
            }
          } else {
            document.getElementById('img_intro').value = '';
            document.getElementById('image_preview').classList.add('hidden');
          }
        });

        // Fetch and populate account details
        if (accountId) {
          const account = await fetchAccount(accountId);
          if (account) {
            populateForm(account);
          }
        } else {
          showError('Account ID not found in URL');
        }

        // Back button
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/accounts';
        });

        // Form submission
        document.getElementById('editAccountForm').addEventListener('submit', async function(e) {
          e.preventDefault();

          document.getElementById('errorMessage').classList.add('hidden');
          document.getElementById('successMessage').classList.add('hidden');
          document.getElementById('submitButton').disabled = true;
          document.getElementById('submitButton').textContent = 'Updating...';

          // Create a direct JavaScript object instead of FormData
          const formData = {
            website_url: document.getElementById('website_url').value,
            username: document.getElementById('username').value || '',
            login_cookie: parseInt(document.getElementById('login_cookie').value, 10),
            name: document.getElementById('name').value || '',
            description: document.getElementById('description').value || '',
            sort: parseInt(document.getElementById('sort').value, 10) || 0
          };

          // Add the Base64 image if one was converted
          const base64Image = document.getElementById('img_intro').value;
          if (base64Image) {
            formData.img_intro = base64Image;
          }

          // Only include password if it's not empty
          const password = document.getElementById('password').value;
          if (password) {
            formData.password = password;
          }

          await updateAccount(accountId, formData);
        });
      });
    </script>
  </body>
</html>

