<%- include('../partials/layout', {
  title: 'Dashboard',
  subtitle: 'Welcome to the Kitsify Admin Dashboard',
  currentPath: '/admin',
  body: `
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white shadow rounded-lg p-6 flex flex-col items-center justify-center text-center hover:shadow-lg transition-shadow">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-blue-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        <h2 class="text-xl font-semibold mb-2">Account Management</h2>
        <p class="text-gray-600 mb-4">Manage user accounts, credentials, and permissions</p>
        <a href="/admin/accounts" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg w-full transition-colors">
          Manage Accounts
        </a>
      </div>

      <div class="bg-white shadow rounded-lg p-6 flex flex-col items-center justify-center text-center hover:shadow-lg transition-shadow">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-green-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
        <h2 class="text-xl font-semibold mb-2">Package Management</h2>
        <p class="text-gray-600 mb-4">Manage subscription packages and assign them to users</p>
        <a href="/admin/packages" class="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg w-full transition-colors">
          Manage Packages
        </a>
      </div>

      <div class="bg-white shadow rounded-lg p-6 flex flex-col items-center justify-center text-center hover:shadow-lg transition-shadow">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-purple-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <h2 class="text-xl font-semibold mb-2">Trial User Management</h2>
        <p class="text-gray-600 mb-4">Create and manage trial user accounts</p>
        <a href="/admin/trial-users" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg w-full transition-colors">
          Manage Trial Users
        </a>
      </div>

      <div class="bg-white shadow rounded-lg p-6 flex flex-col items-center justify-center text-center hover:shadow-lg transition-shadow">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-amber-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <h2 class="text-xl font-semibold mb-2">Cookie Management</h2>
        <p class="text-gray-600 mb-4">Manage cookies for accounts across different servers</p>
        <a href="/admin/cookies" class="bg-amber-500 hover:bg-amber-700 text-white font-bold py-3 px-6 rounded-lg w-full transition-colors">
          Manage Cookies
        </a>
      </div>
    </div>
  `
}) %>
