<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - Kitsify Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      .fade-in {
        animation: fadeIn 0.3s ease-in-out;
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col items-center justify-center p-4">
    <div class="max-w-md w-full mx-auto">
      <!-- Logo -->
      <div class="text-center mb-8">
        <img src="https://kitsify.com/assets/images/logo_kitsify.png" alt="Kitsify Logo" class="h-16 w-auto mx-auto mb-2">
        <h2 class="text-3xl font-bold text-gray-900">Kitsify Admin</h2>
        <p class="text-gray-600 mt-1">Sign in to access the admin dashboard</p>
      </div>

      <!-- Login Form -->
      <div class="bg-white rounded-xl shadow-lg overflow-hidden fade-in">
        <div class="p-6 sm:p-8">
          <h1 class="text-2xl font-bold text-gray-800 mb-6">Sign In</h1>

          <div id="errorMessage" class="hidden mb-6 p-4 text-red-700 bg-red-50 rounded-lg border border-red-200"></div>

          <form id="loginForm" class="space-y-5">
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                </div>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  class="pl-10 block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <input
                  type="password"
                  id="password"
                  name="password"
                  required
                  class="pl-10 block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="••••••••"
                />
              </div>
            </div>

            <button
              type="submit"
              id="submitButton"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Sign in
            </button>
          </form>
        </div>
      </div>

      <!-- Footer -->
      <div class="mt-8 text-center text-sm text-gray-600">
        &copy; <%= new Date().getFullYear() %> Kitsify. All rights reserved.
      </div>
    </div>

    <script>
      const errorMessage = document.getElementById('errorMessage');
      const submitButton = document.getElementById('submitButton');
      const loginForm = document.getElementById('loginForm');

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        submitButton.disabled = false;
        submitButton.textContent = 'Sign in';
      }

      loginForm.addEventListener('submit', async function (e) {
        e.preventDefault();

        errorMessage.classList.add('hidden');
        submitButton.disabled = true;
        submitButton.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Signing in...
        `;

        try {
          // Set withCredentials to true to allow cookies to be sent
          const response = await axios.post('/login', {
            email: document.getElementById('email').value,
            password: document.getElementById('password').value,
          }, { withCredentials: true });

          if (response.data.success) {
            window.location.href = '/';
          } else {
            showError('Invalid response from server');
          }
        } catch (error) {
          const message =
            error.response?.data?.message ||
            'Login failed. Please check your credentials.';
          showError(message);
        }
      });

      // Try to refresh token if needed
      async function refreshToken() {
        try {
          const response = await axios.post('/auth/refresh', {}, { withCredentials: true });
          return response.data.success;
        } catch (error) {
          console.error('Token refresh failed:', error);
          return false;
        }
      }

      // Check if user is already logged in using cookie
      async function checkAuthStatus() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (response.data.isAuthenticated) {
            window.location.href = '/';
          }
        } catch (error) {
          // If unauthorized, try to refresh token
          if (error.response && error.response.status === 401) {
            const refreshSuccess = await refreshToken();
            if (refreshSuccess) {
              // Check auth status again after refresh
              const retryResponse = await axios.get('/auth/status', { withCredentials: true });
              if (retryResponse.data.isAuthenticated) {
                window.location.href = '/';
              }
            }
          } else {
            console.error('Error checking auth status:', error);
          }
        }
      }

      // Check auth status when page loads
      checkAuthStatus();
    </script>
  </body>
</html>
