<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Change Password - Kitsify Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      .fade-in {
        animation: fadeIn 0.3s ease-in-out;
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col items-center justify-center p-4">
    <div class="max-w-md w-full mx-auto">
      <!-- Logo -->
      <div class="text-center mb-8">
        <img src="https://kitsify.com/assets/images/logo_kitsify.png" alt="Kitsify Logo" class="h-16 w-auto mx-auto mb-2">
        <h2 class="text-3xl font-bold text-gray-900">Kitsify Admin</h2>
        <p class="text-gray-600 mt-1">Change your admin password</p>
      </div>

      <!-- Change Password Form -->
      <div class="bg-white rounded-xl shadow-lg overflow-hidden fade-in">
        <div class="p-6 sm:p-8">
          <h1 class="text-2xl font-bold text-gray-800 mb-6">Change Password</h1>

          <div id="successMessage" class="hidden mb-6 p-4 text-green-700 bg-green-50 rounded-lg border border-green-200"></div>
          <div id="errorMessage" class="hidden mb-6 p-4 text-red-700 bg-red-50 rounded-lg border border-red-200"></div>

          <form id="changePasswordForm" class="space-y-5">
            <div>
              <label for="currentPassword" class="block text-sm font-medium text-gray-700 mb-1">Current Password</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <input
                  type="password"
                  id="currentPassword"
                  name="currentPassword"
                  required
                  class="pl-10 block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="••••••••"
                />
              </div>
            </div>

            <div>
              <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <input
                  type="password"
                  id="newPassword"
                  name="newPassword"
                  required
                  class="pl-10 block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="••••••••"
                />
              </div>
              <p class="mt-1 text-xs text-gray-500">Password must be at least 6 characters with 1 uppercase, 1 lowercase, and 1 number or special character.</p>
            </div>

            <div>
              <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  required
                  class="pl-10 block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="••••••••"
                />
              </div>
            </div>

            <button
              type="submit"
              id="submitButton"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Change Password
            </button>

            <div class="text-center">
              <a href="/" class="text-sm text-blue-600 hover:text-blue-800">Back to Dashboard</a>
            </div>
          </form>
        </div>
      </div>

      <!-- Footer -->
      <div class="mt-8 text-center text-sm text-gray-600">
        &copy; <%= new Date().getFullYear() %> Kitsify. All rights reserved.
      </div>
    </div>

    <script>
      const errorMessage = document.getElementById('errorMessage');
      const successMessage = document.getElementById('successMessage');
      const submitButton = document.getElementById('submitButton');
      const changePasswordForm = document.getElementById('changePasswordForm');

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        successMessage.classList.add('hidden');
        submitButton.disabled = false;
        submitButton.textContent = 'Change Password';
      }

      function showSuccess(message) {
        successMessage.textContent = message;
        successMessage.classList.remove('hidden');
        errorMessage.classList.add('hidden');
        submitButton.disabled = false;
        submitButton.textContent = 'Change Password';
        // Reset form
        changePasswordForm.reset();
      }

      changePasswordForm.addEventListener('submit', async function (e) {
        e.preventDefault();

        // Check if passwords match
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (newPassword !== confirmPassword) {
          showError('New password and confirm password do not match');
          return;
        }

        errorMessage.classList.add('hidden');
        successMessage.classList.add('hidden');
        submitButton.disabled = true;
        submitButton.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Changing Password...
        `;

        try {
          const response = await axios.post('/change-password', {
            currentPassword: document.getElementById('currentPassword').value,
            newPassword: document.getElementById('newPassword').value,
            confirmPassword: document.getElementById('confirmPassword').value,
          }, { withCredentials: true });

          if (response.data && response.data.message) {
            showSuccess(response.data.message);
          } else {
            showSuccess('Password changed successfully');
          }
        } catch (error) {
          const message =
            error.response?.data?.message ||
            'Failed to change password. Please try again.';
          showError(message);
        }
      });
    </script>
  </body>
</html>
