<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><%= title %> - Kitsify Admin</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  
  <!-- Include auth script -->
  <%- include('./auth-script') %>
  
  <style>
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
    
    /* Modal styles */
    .modal {
      transition: opacity 0.25s ease;
    }
    body.modal-active {
      overflow-x: hidden;
      overflow-y: visible !important;
    }
    
    /* Fade-in animation */
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .fade-in {
      animation: fadeIn 0.3s ease-in-out;
    }
  </style>
  
  <!-- Additional head content -->
  <% if (typeof additionalHead !== 'undefined') { %>
    <%- additionalHead %>
  <% } %>
</head>
<body class="bg-gray-100 min-h-screen">
  <!-- Check authentication on page load -->
  <script>
    document.addEventListener('DOMContentLoaded', async () => {
      setupAxios();
      await checkAuth();
    });
  </script>
  
  <!-- Include header -->
  <%- include('./header') %>
  
  <!-- Include sidebar -->
  <%- include('./sidebar', { currentPath: currentPath }) %>
  
  <!-- Main content -->
  <main class="lg:ml-64 pt-16 min-h-screen fade-in">
    <div class="p-4 md:p-6 lg:p-8">
      <!-- Page header -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900"><%= title %></h1>
        <% if (typeof subtitle !== 'undefined') { %>
          <p class="mt-1 text-sm text-gray-500"><%= subtitle %></p>
        <% } %>
      </div>
      
      <!-- Page content -->
      <%- body %>
    </div>
  </main>
  
  <!-- Overlay for mobile sidebar -->
  <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden lg:hidden"></div>
  
  <!-- Additional scripts -->
  <script>
    // Handle sidebar overlay
    document.addEventListener('DOMContentLoaded', function() {
      const sidebarOverlay = document.getElementById('sidebar-overlay');
      const sidebar = document.getElementById('sidebar');
      
      if (sidebarOverlay && sidebar) {
        // Show overlay when sidebar is visible on mobile
        const toggleOverlay = () => {
          if (sidebar.classList.contains('-translate-x-full')) {
            sidebarOverlay.classList.add('hidden');
          } else {
            sidebarOverlay.classList.remove('hidden');
          }
        };
        
        // Initial state
        toggleOverlay();
        
        // Watch for sidebar changes
        const observer = new MutationObserver(toggleOverlay);
        observer.observe(sidebar, { attributes: true, attributeFilter: ['class'] });
        
        // Close sidebar when overlay is clicked
        sidebarOverlay.addEventListener('click', function() {
          sidebar.classList.add('-translate-x-full');
        });
      }
    });
    
    // Toast notification helper
    function showToast(message, type = 'success') {
      Toastify({
        text: message,
        duration: 3000,
        close: true,
        gravity: "top",
        position: "right",
        backgroundColor: type === 'success' ? "#4CAF50" : 
                         type === 'error' ? "#F44336" : 
                         type === 'warning' ? "#FF9800" : "#2196F3",
      }).showToast();
    }
  </script>
  
  <!-- Additional body scripts -->
  <% if (typeof additionalScripts !== 'undefined') { %>
    <%- additionalScripts %>
  <% } %>
</body>
</html>
