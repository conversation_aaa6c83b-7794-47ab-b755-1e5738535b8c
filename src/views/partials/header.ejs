<header class="bg-white shadow-md fixed top-0 left-0 right-0 z-50">
  <div class="flex justify-between items-center px-4 py-3 lg:px-6">
    <!-- Logo and brand -->
    <div class="flex items-center">
      <button id="sidebar-toggle" class="mr-2 text-gray-600 hover:text-gray-900 lg:hidden">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
      <a href="/admin" class="flex items-center">
        <img src="https://kitsify.com/assets/images/logo_kitsify.png" alt="Kitsify Logo" class="h-8 w-auto mr-2">
        <span class="text-xl font-semibold text-gray-900">Kitsify Admin</span>
      </a>
    </div>

    <!-- User menu -->
    <div class="relative" id="user-menu-container">
      <button id="user-menu-button" class="flex items-center text-gray-700 hover:text-gray-900 focus:outline-none">
        <span class="hidden md:block mr-2 text-sm font-medium">Admin</span>
        <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </div>
      </button>

      <!-- Dropdown menu -->
      <div id="user-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden">
        <a href="/change-password" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Change Password</a>
        <div class="border-t border-gray-100"></div>
        <button id="logout-button" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
          Logout
        </button>
      </div>
    </div>
  </div>
</header>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Toggle sidebar on mobile
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.getElementById('sidebar');

    if (sidebarToggle && sidebar) {
      sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('-translate-x-full');
      });
    }

    // User dropdown toggle
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdown = document.getElementById('user-dropdown');

    if (userMenuButton && userDropdown) {
      userMenuButton.addEventListener('click', function() {
        userDropdown.classList.toggle('hidden');
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', function(event) {
        const isClickInside = userMenuButton.contains(event.target) || userDropdown.contains(event.target);
        if (!isClickInside && !userDropdown.classList.contains('hidden')) {
          userDropdown.classList.add('hidden');
        }
      });
    }

    // Logout functionality
    const logoutButton = document.getElementById('logout-button');
    if (logoutButton) {
      logoutButton.addEventListener('click', async function() {
        try {
          // Show loading state
          logoutButton.innerHTML = `
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Logging out...
          `;

          // Call the logout endpoint
          const response = await axios.post('/logout', {}, { withCredentials: true });

          if (response.data.success) {
            // Notify any extensions that might be listening
            if (window.chrome && chrome.runtime && chrome.runtime.sendMessage) {
              try {
                chrome.runtime.sendMessage({ action: 'logout' });
              } catch (chromeError) {
                console.log('Chrome messaging not available or failed');
              }
            }

            // Redirect to login page
            window.location.href = '/login';
          } else {
            logoutButton.textContent = 'Logout';
            console.error('Logout failed: Server returned unsuccessful response');
          }
        } catch (error) {
          logoutButton.textContent = 'Logout';
          console.error('Logout failed:', error);
          alert('Logout failed. Please try again.');
        }
      });
    }
  });
</script>
