<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Joining Discord Server - Kitsify</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f9fafb;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    }
    .discord-bg {
      background-color: #5865F2;
    }
    .loader {
      border: 5px solid #f3f3f3;
      border-top: 5px solid #5865F2;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .steps-container {
      position: relative;
    }
    .step {
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      position: relative;
    }
    .step-number {
      display: inline-block;
      width: 30px;
      height: 30px;
      background-color: #5865F2;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 30px;
      margin-right: 10px;
      font-weight: bold;
    }
    .step-active {
      border: 2px solid #5865F2;
      background-color: #EEF2FF;
    }
    .step-pending {
      border: 2px solid #E5E7EB;
      background-color: #F9FAFB;
      opacity: 0.7;
    }
  </style>
  <script>
    // Open Discord invite in a new tab
    function openDiscordInvite() {
      window.open("<%= inviteUrl %>", "_blank");
    }
  </script>
</head>
<body>
  <div class="min-h-screen flex items-center justify-center px-4">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg overflow-hidden">
      <div class="discord-bg p-4 flex justify-center">
        <img src="https://assets-global.website-files.com/6257adef93867e50d84d30e2/636e0a6a49cf127bf92de1e2_icon_clyde_white_RGB.png" alt="Discord Logo" class="h-16">
      </div>
      <div class="p-8">
        <h1 class="text-2xl font-bold text-center text-gray-800 mb-6">Join Discord Server</h1>

        <div class="steps-container mb-6">
          <div class="step step-active">
            <div class="flex items-center mb-2">
              <span class="step-number">1</span>
              <h3 class="font-bold text-lg">Join Discord Server</h3>
            </div>
            <p class="text-gray-600 ml-10 mb-4">A new window will open for you to join the Discord server.</p>
            <p class="text-gray-600 ml-10 mb-4"><strong>Please click the button below to join the Discord server.</strong></p>
            <div class="ml-10">
              <button onclick="openDiscordInvite()" class="discord-bg text-white font-bold py-2 px-6 rounded-full hover:bg-indigo-700 transition duration-300">
                Open Discord
              </button>
            </div>
            <div class="ml-10 mt-4">
              <a href="<%= redirectUrl %>" class="hidden bg-green-500 text-white font-bold py-2 px-6 rounded-full hover:bg-green-600 transition duration-300">
                Complete and Get Access
              </a>
            </div>
          </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-8">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div>
              <h4 class="font-medium text-blue-800">Information</h4>
              <p class="text-sm text-blue-700 mt-1">After joining the Discord server, you will automatically be assigned access permissions. If permissions are not automatically assigned, you can <a href="<%= redirectUrl %>" class="text-blue-800 underline">click here</a> to complete the process.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-4 py-3 text-center">
        <p class="text-sm text-gray-600">
          &copy; <%= new Date().getFullYear() %> Kitsify. All rights reserved.
        </p>
      </div>
    </div>
  </div>
</body>
</html>
