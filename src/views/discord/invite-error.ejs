<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Discord Invite Error - Kitsify</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f9fafb;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    }
    .discord-bg {
      background-color: #5865F2;
    }
  </style>
</head>
<body>
  <div class="min-h-screen flex items-center justify-center px-4">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg overflow-hidden">
      <div class="discord-bg p-4 flex justify-center">
        <img src="https://assets-global.website-files.com/6257adef93867e50d84d30e2/636e0a6a49cf127bf92de1e2_icon_clyde_white_RGB.png" alt="Discord Logo" class="h-16">
      </div>
      <div class="p-8">
        <div class="flex justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
        <h1 class="text-2xl font-bold text-center text-gray-800 mb-4">Đã xảy ra lỗi</h1>
        <p class="text-center text-gray-600 mb-6">
          <%= error || 'Không thể xử lý lời mời Discord của bạn. Vui lòng thử lại sau hoặc liên hệ với bộ phận hỗ trợ.' %>
        </p>
        <div class="flex justify-center space-x-4">
          <a href="https://discord.gg/<%= process.env.DISCORD_INVITE_CODE %>" class="discord-bg text-white font-bold py-2 px-6 rounded-full hover:bg-indigo-700 transition duration-300">
            Tham gia Discord
          </a>
          <a href="mailto:<EMAIL>" class="bg-gray-200 text-gray-800 font-bold py-2 px-6 rounded-full hover:bg-gray-300 transition duration-300">
            Liên hệ hỗ trợ
          </a>
        </div>
      </div>
      <div class="bg-gray-50 px-4 py-3 text-center">
        <p class="text-sm text-gray-600">
          &copy; <%= new Date().getFullYear() %> Kitsify. Tất cả các quyền được bảo lưu.
        </p>
      </div>
    </div>
  </div>
</body>
</html>
