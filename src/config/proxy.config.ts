export const proxyConfig = {
  host: process.env.PROXY_HOST || 'localhost',
  port: parseInt(process.env.PROXY_PORT || '8080', 10),
  protocol: process.env.PROXY_PROTOCOL || 'http',
  username: process.env.PROXY_USERNAME,
  password: process.env.PROXY_PASSWORD,
  userAgent:
    process.env.PROXY_USER_AGENT ||
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
};
